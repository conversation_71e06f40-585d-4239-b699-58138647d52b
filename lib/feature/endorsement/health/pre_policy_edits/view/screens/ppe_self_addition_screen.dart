import 'package:acko_flutter/common/util/input_field_controllers.dart';
import 'package:acko_flutter/common/util/input_field_extension.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:acko_flutter/common/view/app_bar.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/edit_bloc/ppe_edit_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/edit_bloc/ppe_edit_states.dart';
import 'package:design_module/typography/typography.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/uikit/widgets/button/uikit_button.dart';
import 'package:design_module/uikit/widgets/input_field/acko_input_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PpeSelfAdditionScreen extends StatefulWidget {
  @override
  _PpeSelfAdditionScreenState createState() => _PpeSelfAdditionScreenState();
}

class _PpeSelfAdditionScreenState extends State<PpeSelfAdditionScreen> {
  late final HeightEditingController _heightEditingController;
  late final WeightEditingController _weightController;
  PPEEditCubit? _cubit;

  @override
  void initState() {
    super.initState();
    _heightEditingController = HeightEditingController();
    _weightController = WeightEditingController();
    _cubit = BlocProvider.of<PPEEditCubit>(context);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<PPEEditCubit, PPEEditState>(
      listener: (context, state) {
        if (state is SelfAdditionSuccess) {
          // Navigate back to edit screen
          Navigator.pop(context);
        } else if (state is ErrorToast) {
          // Show error message if needed
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Something went wrong')),
          );
        }
      },
      child: Scaffold(
        appBar: getAppBar('', context),
        bottomNavigationBar: Padding(
          padding: const EdgeInsets.all(16),
          child: BlocBuilder<PPEEditCubit, PPEEditState>(
            builder: (context, state) {
              bool isLoading =
                  state is Loading || state is ShowFormEditingLoader;
              return AckoDarkButtonFullWidth(
                text: continue_string,
                buttonState: isLoading
                    ? UIKitButtonState.loading
                    : (_isFormValid()
                        ? UIKitButtonState.active
                        : UIKitButtonState.disabled),
                onTap: () {
                  if (_isFormValid() && !isLoading) {
                    _cubit?.addProposerSelfToPolicy(
                      height: _heightEditingController.text,
                      weight: _weightController.text,
                    );
                  }
                },
              );
            },
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              AckoTextConfig.i.headingMedium.text(
                addMeToCoverage,
              ),
              const SizedBox(height: 24.0),

              AckoTextFormField().heightInputField(
                  controller: _heightEditingController,
                  placeholder: height,
                  onChanged: (value) => setState(() {})),
              const SizedBox(height: 16.0),
              AckoTextFormField().weightInputField(
                  controller: _weightController,
                  placeholder: weight,
                  onChanged: (value) => setState(() {})),
            ],
          ),
        ),
      ),
    );
  }

  bool _isFormValid() {
    return _heightEditingController.text.isNotEmpty &&
        _weightController.text.isNotEmpty;
  }

  @override
  void dispose() {
    _heightEditingController.dispose();
    _weightController.dispose();
    super.dispose();
  }
}
