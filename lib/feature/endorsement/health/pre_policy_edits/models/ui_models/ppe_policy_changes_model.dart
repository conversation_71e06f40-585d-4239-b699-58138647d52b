import 'dart:math';

import 'package:acko_flutter/feature/endorsement/acko_doc_upload/model/acko_doc_model.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_utils.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/models/health_jm_response.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_repo.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_form_editing_models.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/utils.dart';
import 'package:intl/intl.dart';

import 'ppe_premium_changes_model.dart';

class PolicyChangesData {
  ChangedValues? portingChanges;
  List<ChangedValues>? policyChanges = [];
  List<ChangedValues>? memberChanges = [];
  List<ChangedValues>? updatedMembersList = [];
  PremiumChange? premiumChange;
  double? amount;

  bool? isBasbaProposal;

  int get totalEdits {
    return (portingChanges != null ? 1 : 0) +
        (policyChanges?.length ?? 0) +
        (memberChanges?.length ?? 0) +
        (updatedMembersList?.length ?? 0);
  }

  bool get hasCheckBox {
    if (memberChanges.isNullOrEmpty) return false;

    for (var element in memberChanges!) {
      if (element.confirmationNeeded) {
        return true;
      }
    }
    return false;
  }

  bool get hasDocUploadRequired {
    if (memberChanges.isNullOrEmpty) return false;

    for (var element in memberChanges!) {
      if (element.documents != null) {
        return true;
      }
    }
    return false;
  }

  void amountToBePaid(double? amt) {
    amount = amt;
  }

  mapToPremiumChange(DeltaPremium? deltaPremium) {
    if (deltaPremium != null) {
      premiumChange = PremiumChange(
        oldPremium: deltaPremium.previousInstallment,
        newPremium: deltaPremium.newInstallment,
        paymentFrequency: deltaPremium.paymentFrequency,
      );
    } else
      premiumChange = null;
  }

  /// int
  String get toBePaidFormatted {
    if (amount == null) {
      return '';
    }

    return '₹${NumberUtils.commaSeparatedNumber((amount!.toInt().abs()))}';
  }

  /// int
  String get footerAmountToBePaid {
    if (amount == null) {
      return '';
    }
    return '₹${NumberUtils.commaSeparatedNumber(amount! > 0 ? amount!.toInt() : amount!.toInt().abs())}';
  }

  String getFooterTitle(bool isBasbaProposal) {
    this.isBasbaProposal = isBasbaProposal;
    if (amount == null || amount?.toInt() == 0) return '';
    if ((amount ?? 0) < 0) {
      return isBasbaProposal ? 'Release amount' : 'Refund amount';
    }
    final premiumIncreaseText =
        'Additional premium for ' + (totalEdits > 1 ? 'edits' : 'edit');
    return premiumIncreaseText;
  }

  bool get isRefundRequired {
    return amount != null && amount! < 0;
  }

  bool get isNonFinancial {
    return amount == null || amount == 0;
  }

  String getReviewCtaText(bool createMandate) {
    if (createMandate) {
      return "Set up a new mandate";
    } else if (isNonFinancial) {
      return "Submit";
    } else {
      return "Continue";
    }
  }

  List<Documents> getAllUploadedDocuments() {
    List<Documents> uploadedDocuments = [];

    /// Combine all the changes lists into a single list
    List<ChangedValues>? allChanges = [
      ...?policyChanges,
      ...?memberChanges,
      ...?updatedMembersList,
    ];

    for (var change in allChanges) {
      change.documents?.documentModel?.forEach((docModel) {
        if (docModel.isUploaded == true) {
          uploadedDocuments.addAll(docModel.documents ?? []);
        }
      });
    }

    return uploadedDocuments;
  }

  List<ChangedValues?> get mergeChangesData {
    List<ChangedValues?> mergedList = [];

    if (portingChanges != null) {
      mergedList.add(portingChanges);
    }

    if (policyChanges != null) {
      mergedList.addAll(policyChanges!);
    }

    if (memberChanges != null) {
      mergedList.addAll(memberChanges!);
    }

    if (updatedMembersList != null) {
      mergedList.addAll(updatedMembersList!);
    }

    return mergedList;
  }

  void addChangesFromRenewalRequest(
      RenewalFilteredEndorsements? filteredEndorsements,
      Map<String, String?> insuredNameMap,
      List<Documents>? allDocuments) {
    if (filteredEndorsements!.memberRemoval.isNotNullOrEmpty) {
      for (Endorsements endorsement in filteredEndorsements.memberRemoval!) {
        addChangedValue(endorsement, insuredNameMap, allDocuments);
      }
    }
    if (filteredEndorsements.personalInfo.isNotNullOrEmpty) {
      for (int i = 0; i < filteredEndorsements.personalInfo!.length; i++) {
        addChangedValue(filteredEndorsements.personalInfo![i], insuredNameMap,
            allDocuments);
      }
    }
    if (filteredEndorsements.familyMemberInfo.isNotNullOrEmpty) {
      for (int i = 0; i < filteredEndorsements.familyMemberInfo!.length; i++) {
        addChangedValue(filteredEndorsements.familyMemberInfo![i],
            insuredNameMap, allDocuments);
      }
    }
    if (filteredEndorsements.memberAddition.isNotNullOrEmpty) {
      for (int i = 0; i < filteredEndorsements.memberAddition!.length; i++) {
        addChangedValue(filteredEndorsements.memberAddition![i], insuredNameMap,
            allDocuments);
      }
    }
  }

  void addChangedValue(Endorsements endo, Map<String, String?> insuredNameMap,
      List<Documents>? allDocuments) {
    String? insuredId;
    if (endo.endorsementType == "member_addition") {
      insuredId = endo.newValue.first['insured_id'];
    } else if (endo.endorsementType == "member_removal") {
      insuredId = endo.oldValue.first['insured_id'];
    } else {
      insuredId = endo.parameters?["insured_id"];
    }

    bool confirmationNeeded = endo.endorsementType == 'name_change';

    String? name = insuredNameMap[insuredId];

    /// Determine the change title based on the endorsement type
    String changeTitle = _getChangeTitle(endo.endorsementType, name);

    /// Process oldValue and newValue, considering they might be maps
    dynamic oldVal = endo.oldValue;
    dynamic newVal = endo.newValue;

    if (endo.endorsementType == "height_change") {
      oldVal = HealthJourneyManagerUtils()
          .fromAckoHeightToInchFeet(endo.oldValue.toString());
      newVal = HealthJourneyManagerUtils()
          .fromAckoHeightToInchFeet(endo.newValue.toString());
    } else if (endo.endorsementType == "weight_change") {
      oldVal = "${endo.oldValue} kg";
      newVal = "${endo.newValue} kg";
    }

    String? endorsementType = endo.endorsementType;

    /// Collect related documents
    AckoDocumentModel? documents = _generateDocumentsForEditId(null,
        endo.docsRequired, allDocuments, insuredId, null, null, endorsementType,
        journeyType: JourneyType.RENEWAL);

    /// Handles different types of changes
    switch (endo.endorsementType) {
      case 'member_addition':
        updatedMembersList!.add(ChangedValues(
          changeTitle: changeTitle,
          updatedMemberTitle: _extractMemberName(newVal),
          updatedMemberSubtitle: _formatMemberDetails(newVal),
          documentsRequired: endo.requirements?.docsRequired,
          insuredId: insuredId,
          documents: documents,
        ));
        break;

      case 'member_removal':
        updatedMembersList!.add(ChangedValues(
          changeTitle: changeTitle,
          updatedMemberTitle: _extractMemberName(oldVal),
          updatedMemberSubtitle: _formatMemberDetails(oldVal),
          documentsRequired: endo.requirements?.docsRequired,
          insuredId: insuredId,
          documents: documents,
        ));
        break;

      default:
        memberChanges!.add(ChangedValues(
            changeTitle: changeTitle,
            changeOldValue: oldVal,
            changeNewValue: newVal,
            documentsRequired: endo.docsRequired,
            insuredId: insuredId,
            documents: documents,
            confirmationNeeded: confirmationNeeded));
        break;
    }
  }

  void addChangesFromEditRequest(
      List<EditRequest>? editRequest,
      List<Documents>? allDocuments,
      Map<String, String?> insuredNameMap,
      EntityDetails? entityDetails) {
    if (editRequest.isNullOrEmpty) return;

    for (var edit in editRequest!) {
      if (edit.endorsements == null || edit.endorsements!.isEmpty) continue;
      bool confirmationNeeded = hasUserIdAndNameChange(
          edit.endorsements, edit.requirements?.docsRequired); // not neeeded

      var endorsement = edit.endorsements!.first;
      String? insuredId;

      if (endorsement.endorsementType == "member_addition") {
        insuredId = endorsement.newValue.first['insured_id'];
      } else if (endorsement.endorsementType == "member_removal") {
        insuredId = endorsement.oldValue['insured_id'];
      } else {
        insuredId = endorsement.parameters?["insured_id"];
      }

      String? name = insuredNameMap[insuredId];

      /// Determine the change title based on the endorsement type
      String changeTitle = _getChangeTitle(endorsement.endorsementType, name);

      /// Process oldValue and newValue, considering they might be maps
      dynamic oldVal = endorsement.oldValue;
      dynamic newVal = endorsement.newValue;

      if (endorsement.endorsementType == "height_change") {
        oldVal = HealthJourneyManagerUtils()
                .fromAckoHeightToInchFeet(endorsement.oldValue.toString()) +
            " ft";
        newVal = HealthJourneyManagerUtils()
                .fromAckoHeightToInchFeet(endorsement.newValue.toString()) +
            " ft";
      } else if (endorsement.endorsementType == "weight_change") {
        oldVal = "${endorsement.oldValue} kg";
        newVal = "${endorsement.newValue} kg";
      } else if (endorsement.endorsementType == "gender_change") {
        oldVal = endorsement.oldValue.toString().toSentenceCase();
        newVal = endorsement.newValue.toString().toSentenceCase();
      } else if (endorsement.endorsementType == "porting_date_change") {
        String parsedOldDate = _convertDateFormat(endorsement.oldValue);
        String parsedNewDate = _convertDateFormat(endorsement.newValue);
        oldVal =
            HealthJourneyManagerUtils().convertHyphenToSlash(parsedOldDate);
        newVal =
            HealthJourneyManagerUtils().convertHyphenToSlash(parsedNewDate);
      } else if (endorsement.endorsementType == "dob_change") {
        oldVal = HealthJourneyManagerUtils()
            .convertHyphenToSlash(endorsement.oldValue);
        newVal = HealthJourneyManagerUtils()
            .convertHyphenToSlash(endorsement.newValue);
      }

      String? editId = edit.id;
      String? editGroupId = edit.editGroupId;

      String? endorsementType = edit.endorsementType;

      /// Collect related documents
      AckoDocumentModel? documents = _generateDocumentsForEditId(
          editId,
          edit.requirements?.docsRequired,
          allDocuments,
          insuredId,
          editGroupId,
          entityDetails,
          endorsementType);

      /// Handles different types of changes
      switch (endorsement.endorsementType) {
        case 'porting_date_change':
          portingChanges = ChangedValues(
            changeTitle: changeTitle,
            changeOldValue: oldVal,
            changeNewValue: newVal,
            documentsRequired: endorsement.requirements?.docsRequired,
            insuredId: insuredId,
            editId: editId,
            documents: documents,
          );
          break;

        case 'plan_name_change':
          policyChanges!.add(ChangedValues(
            changeTitle: changeTitle,
            changeOldValue: oldVal.toString(),
            changeNewValue: newVal.toString(),
            documentsRequired: endorsement.requirements?.docsRequired,
            insuredId: insuredId,
            editId: editId,
            documents: documents,
          ));
          break;

        case 'deductible_change':
        case 'sum_insured_change':
          policyChanges!.add(ChangedValues(
            changeTitle: changeTitle,
            changeOldValue: HealthJourneyManagerUtils()
                .formatCurrencyWithUnits(oldVal.toString(), fullUnit: false),
            changeNewValue: HealthJourneyManagerUtils()
                .formatCurrencyWithUnits(newVal.toString(), fullUnit: false),
            documentsRequired: endorsement.requirements?.docsRequired,
            insuredId: insuredId,
            editId: editId,
            documents: documents,
          ));
          break;

        case 'member_addition':
          updatedMembersList!.add(ChangedValues(
            changeTitle: changeTitle,
            updatedMemberTitle: _extractMemberName(newVal),
            updatedMemberSubtitle: _formatMemberDetails(newVal),
            documentsRequired: endorsement.requirements?.docsRequired,
            insuredId: insuredId,
            editId: editId,
            documents: documents,
          ));
          break;

        case 'member_removal':
          updatedMembersList!.add(ChangedValues(
            changeTitle: changeTitle,
            updatedMemberTitle: _extractMemberName(oldVal),
            updatedMemberSubtitle: _formatMemberDetails(oldVal),
            documentsRequired: endorsement.requirements?.docsRequired,
            insuredId: insuredId,
            editId: editId,
            documents: documents,
          ));
          break;

        default:
          memberChanges!.add(ChangedValues(
              changeTitle: changeTitle,
              changeOldValue: oldVal,
              changeNewValue: newVal,
              documentsRequired: endorsement.requirements?.docsRequired,
              insuredId: insuredId,
              editId: editId,
              documents: documents,
              confirmationNeeded: confirmationNeeded));
          break;
      }
    }
  }

  String _convertDateFormat(String date) {
    DateTime parsedDate = DateFormat('yyyy-MM-dd').parse(date);
    return DateFormat('dd-MM-yyyy').format(parsedDate);
  }

  bool hasUserIdAndNameChange(
      List<Endorsements>? endorsements, List<String>? docsRequired) {
    if (endorsements == null || endorsements.isEmpty) {
      return false;
    }

    for (var endorsement in endorsements) {
      if (endorsement.parameters?.containsKey('user_id') ?? false) {
        String userId = endorsement.parameters?['user_id'];
        if (userId.isNotNullOrEmpty &&
            endorsement.endorsementType == 'name_change' &&
            docsRequired.isNotNullOrEmpty) {
          // && (endorsement.requirements?.docsRequired.isNotNullOrEmpty ?? false // endorsement.requirements.highlightRequired || uwReviewRequired
          return true;
        }
      }
    }
    return false;
  }

  AckoDocumentModel? _generateDocumentsForEditId(
      String? editId,
      List<String>? docsRequired,
      List<Documents>? allDocuments,
      String? insuredId,
      String? editGroupId,
      EntityDetails? entityDetails,
      String? endorsementType,
      {JourneyType journeyType = JourneyType.PRE_POLICY_EDIT}) {
    /// Added insuredId as a parameter and return null if no documents are required
    if (docsRequired == null || docsRequired.isEmpty) {
      return null;
    }

    /// Map to aggregate documents by their type
    Map<String, List<Documents>> documentsMap = {};

    /// Aggregate documents by their type and editId
    for (Documents doc in allDocuments ?? []) {
      bool docMapCondition = false;
      if (journeyType == JourneyType.RENEWAL) {
        docMapCondition = doc.metadata?.endorsementType?.toLowerCase() ==
                endorsementType?.toLowerCase() &&
            doc.metadata?.memberUniqueId == insuredId;
      } else {
        docMapCondition = doc.metadata?.editGroupId == editGroupId &&
            doc.metadata?.endorsementType == endorsementType &&
            doc.metadata?.memberUniqueId == insuredId;
      }
      if (docMapCondition) {
        String docType = doc.metadata?.documentCategory ?? 'unknown';
        if (docsRequired.contains(docType)) {
          if (!documentsMap.containsKey(docType)) {
            documentsMap[docType] = [];
          }
          documentsMap[docType]!.add(doc);
        }
      }
    }

    /// Create DocumentModel instances based on aggregated documents
    List<DocumentModel> documentModels = [];
    for (String docType in docsRequired) {
      List<Documents>? relatedDocuments = documentsMap[docType];
      bool hasDocuments =
          relatedDocuments != null && relatedDocuments.isNotEmpty;

      if (hasDocuments) {
        /// If we have existing documents, use them
        documentModels.add(DocumentModel(
          editType: docType,
          ctaText: "View/Edit Document", // todo: custom cta text for docs
          ctaIcon: HealthJourneyManagerAssets.uploadIcon,
          isUploaded: true,
          isRequired: true,

          /// Assuming all docs in docsRequired are required
          documents: relatedDocuments,
        ));
      } else {
        /// Create a placeholder document if no existing document is found

        Documents placeholderDoc = Documents(
          id: null,
          entityName: null,
          entityId: null,
          centralReferenceId: null,
          centralReferenceType: null,
          centralDocId: null,
          documentType: null,
          documentFormat: null,
          documentName: null,
          s3Url: null,
          source: null,
          lastModifiedBy: null,
          metadata: Metadata(
              documentName: null,
              proposalId: null,
              fileName: null,
              allowDuplicate: null,
              documentCategory: docType,
              memberUniqueId: insuredId,
              description: null,
              documentFormat: null,
              editId: editId,
              editGroupId: editGroupId,
              endorsementType: endorsementType),
          deleted: null,
          createdAt: null,
          updatedAt: null,
          previewUrl: null,
        );

        documentModels.add(DocumentModel(
          editType: docType,
          ctaText: "Upload Document",
          ctaIcon: HealthJourneyManagerAssets.uploadIcon,
          isUploaded: false,
          isRequired: true,

          /// Assuming all docs in docsRequired are required
          documents: [placeholderDoc],
        ));
      }
    }

    return AckoDocumentModel(
      id: _generateRandomId(10),
      title: _getDocumentTitleForAll(docsRequired),
      description: _getDocumentDescriptionForAll(docsRequired),
      documentModel: documentModels,
    );
  }

  String _generateRandomId(int length) {
    const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = Random();
    return String.fromCharCodes(Iterable.generate(length,
        (_) => characters.codeUnitAt(random.nextInt(characters.length))));
  }

  String _getDocumentTitleForAll(List<String>? docsRequired) {
    return "Upload any of these documents";
  }

  String _getDocumentDescriptionForAll(List<String>? docsRequired) {
    for (String docType in docsRequired!) {
      if ("MARRIAGE_CERTIFICATE".equalsIgnoreCase(docType)) {
        return "Marriage certificate ";
      }
    }
    return "Aadhaar card, driver’s licence, PAN card or passport.";
  }

  String _convertMapToString(List<Map<String, dynamic>> mapList) {
    return mapList
        .map((map) => map.entries
            .map((entry) => '${entry.key}: ${entry.value}')
            .join(', '))
        .join('; ');
  }

  String? _extractMemberName(dynamic value) {
    if (value is String) {
      return value;
    } else if (value is List && value.isNotEmpty) {
      var firstMap = value[0];
      if (firstMap.containsKey('parameters')) {
        var parameters = firstMap['parameters'] as Map<String, dynamic>;
        if (parameters.containsKey('name')) {
          var nameEntry = parameters['name'];
          if (nameEntry is Map && nameEntry.containsKey('value')) {
            return nameEntry['value'].toString();
          } else if (nameEntry is String) {
            return nameEntry;
          }
        }
      }
    } else if (value is Map) {
      var firstMap = value;
      if (firstMap.containsKey('parameters')) {
        var parameters = firstMap['parameters'] as Map<String, dynamic>;
        if (parameters.containsKey('name')) {
          var nameEntry = parameters['name'];
          if (nameEntry is Map && nameEntry.containsKey('value')) {
            return nameEntry['value'].toString();
          } else if (nameEntry is String) {
            return nameEntry;
          }
        }
      }
    }
    return null;
  }

  String _formatMemberDetails(dynamic memberDetails) {
    if (memberDetails is String) {
      return memberDetails;
    } else if (memberDetails is List && memberDetails.isNotEmpty) {
      var firstMap = memberDetails[0];
      if (firstMap.containsKey('parameters')) {
        var parameters = firstMap['parameters'] as Map<String, dynamic>;

        String? relationship = parameters['relation'] is Map
            ? parameters['relation']['value']?.toString()
            : null;
        String? gender = parameters['gender'] is Map
            ? parameters['gender']['value']?.toString().toSentenceCase()
            : null;
        String? dob = parameters['dob'] is Map
            ? parameters['dob']['value']?.toString()
            : null;

        return '($relationship, $gender, ${HealthJourneyManagerUtils().convertHyphenToSlash(dob)})';
      }
    } else if (memberDetails is Map) {
      var firstMap = memberDetails;
      if (firstMap.containsKey('parameters')) {
        var parameters = firstMap['parameters'] as Map<String, dynamic>;

        String? relationship = parameters['relation'] is Map
            ? parameters['relation']['value']?.toString()
            : null;
        String? gender = parameters['gender'] is Map
            ? parameters['gender']['value']?.toString().toSentenceCase()
            : null;
        String? dob = parameters['dob'] is Map
            ? parameters['dob']['value']?.toString()
            : null;

        return '($relationship, $gender, ${HealthJourneyManagerUtils().convertHyphenToSlash(dob)})';
      }
    } else if (memberDetails is MemberDetails) {
      String relation = memberDetails.relation ?? '';
      String gender = (memberDetails.gender ?? '').toSentenceCase();
      String dob = memberDetails.dateOfBirth ?? '';
      return '($relation, $gender, ${HealthJourneyManagerUtils().convertHyphenToSlash(dob)})';
    }
    return '';
  }

  List<Documents> _getDocumentsForEditId(
      String? editId, List<Documents>? allDocuments) {
    if (editId == null || editId.isEmpty || allDocuments.isNullOrEmpty) {
      return [];
    }

    /// Filter documents based on the editId
    return allDocuments!
        .where((document) => document.metadata?.editId == editId)
        .toList();
  }

  /// Helper function to get the change title based on the endorsement type
  String _getChangeTitle(String? endorsementType, String? name) {
    String nameSuffix = (name != null && name.isNotEmpty) ? ' for $name' : '';

    switch (endorsementType) {
      case 'gender_change':
        return 'Gender change$nameSuffix';
      case 'name_change':
        return 'Name change';
      case 'dob_change':
        return 'Date of birth change$nameSuffix';
      case 'relation_change':
        return 'Relation change$nameSuffix';
      case 'address_change':
        return 'Address change$nameSuffix';
      case 'email_change':
        return 'Email change$nameSuffix';
      case 'mobile_number_change':
        return 'Mobile number change$nameSuffix';
      case 'pincode_change':
        return 'Pin code change'; // $nameSuffix
      case 'height_change':
        return 'Height change$nameSuffix';
      case 'weight_change':
        return 'Weight change$nameSuffix';
      case 'sum_insured_change':
        return 'Sum insured change';
      case 'deductible_change':
        return 'Revised deductible';
      case 'plan_name_change':
        return 'Plan name change$nameSuffix';
      case 'porting_date_change':
        return 'Previous policy end date'; // $nameSuffix
      case 'member_addition':
        return 'Member added';
      case 'member_removal':
        return 'Member removed';
      default:
        return '${endorsementType?.replaceAll('_', ' ') ?? 'Change'}$nameSuffix';
    }
  }

  /// getting used in editing screens
  void addChangesFromForm(FormValues formValues) {
    if (formValues.oldValues == null || formValues.newValues == null) return;

    if (formValues.oldValues!.portingDetails?.portingDate !=
        formValues.newValues!.portingDetails?.portingDate) {
      portingChanges = ChangedValues(
        changeTitle: 'Previous policy end date',
        changeOldValue: HealthJourneyManagerUtils().convertHyphenToSlash(
            formValues.oldValues!.portingDetails?.portingDate),
        changeNewValue: HealthJourneyManagerUtils().convertHyphenToSlash(
            formValues.newValues!.portingDetails?.portingDate),
        documentsRequired: [],
      );
    }

    if (formValues.oldValues!.policyDetails?.packageName !=
        formValues.newValues!.policyDetails?.packageName) {
      policyChanges!.add(ChangedValues(
        changeTitle: 'Plan Name change',
        changeOldValue: formValues.oldValues!.policyDetails?.packageName,
        changeNewValue: formValues.newValues!.policyDetails?.packageName,
        documentsRequired: [],
      ));
    }

    if (formValues.oldValues!.policyDetails?.sumInsured?.id != null &&
        formValues.newValues!.policyDetails?.sumInsured?.id != null &&
        formValues.oldValues!.policyDetails!.sumInsured!.id !=
            formValues.newValues!.policyDetails!.sumInsured!.id) {
      policyChanges!.add(ChangedValues(
        changeTitle: 'Sum insured change',
        changeOldValue:
            formValues.oldValues!.policyDetails?.sumInsured?.name.toString(),
        changeNewValue:
            formValues.newValues!.policyDetails?.sumInsured?.name.toString(),
        documentsRequired: [],
      ));
    }

    if (formValues.oldValues!.policyDetails?.deductible?.id != null &&
        formValues.newValues!.policyDetails?.deductible?.id != null &&
        formValues.oldValues!.policyDetails!.deductible!.id !=
            formValues.newValues!.policyDetails!.deductible!.id) {
      policyChanges!.add(ChangedValues(
        changeTitle: 'Deductible change',
        changeOldValue: formValues.oldValues!.policyDetails?.deductible?.name,
        changeNewValue: formValues.newValues!.policyDetails?.deductible?.name,
        documentsRequired: [],
      ));
    }

    /// Compare ProposerDetails
    _compareMemberDetailsAndAddChanges(
        formValues.oldValues!.proposerDetails,
        formValues.newValues!.proposerDetails,
        formValues.newValues!.proposerDetails?.name ?? '');

    /// Compare MemberDetails (excluding proposer to avoid double counting)
    if (formValues.oldValues!.memberDetails != null &&
        formValues.newValues!.memberDetails != null) {
      String? proposerInsuredId =
          formValues.newValues!.proposerDetails?.insuredId;

      for (var oldMember in formValues.oldValues!.memberDetails!) {
        // CRITICAL: Skip proposer to avoid double counting
        if (oldMember?.insuredId == proposerInsuredId) {
          continue;
        }

        var newMember = formValues.newValues!.memberDetails!.firstWhere(
          (newMember) => newMember?.insuredId == oldMember?.insuredId,
          orElse: () => null,
        );

        if (newMember != null) {
          _compareMemberDetailsAndAddChanges(
              oldMember, newMember, newMember.name ?? '');
        }
      }
    }

    // Handle newly added members (excluding proposer to avoid double counting)
    String? proposerInsuredId =
        formValues.newValues!.proposerDetails?.insuredId;

    for (var newMember in formValues.newValues!.memberDetails!) {
      // CRITICAL: Skip proposer to avoid double counting
      if (newMember?.insuredId == proposerInsuredId) {
        continue;
      }

      var oldMember = formValues.oldValues!.memberDetails!.firstWhere(
        (oldMember) => oldMember?.insuredId == newMember?.insuredId,
        orElse: () => null,
      );

      if (oldMember == null) {
        updatedMembersList!.add(ChangedValues(
          changeTitle: 'Member added',
          updatedMemberTitle: newMember?.name,
          updatedMemberSubtitle: _formatMemberDetails(newMember),
          documentsRequired: [],
        ));
      }
    }

    // REMOVED: Redundant loop that was causing duplicate "Member removed" entries
    // Member removal is already handled in the first loop through _compareMemberDetailsAndAddChanges
  }

  void _compareMemberDetailsAndAddChanges(
      MemberDetails? oldMember, MemberDetails? newMember, String name) {
    if (oldMember == null || newMember == null) return;

    // FIXED: For proposer self-exclusion, add to updatedMembersList (member addition/removal section)
    // not to memberChanges (member details section)
    if (newMember.isSelfExcluded && !newMember.wasSelfExcluded) {
      // Add to member addition/removal section instead
      updatedMembersList!.add(ChangedValues(
        changeTitle: 'Member removed',
        updatedMemberTitle: newMember.name,
        updatedMemberSubtitle: _formatMemberDetails(newMember),
        documentsRequired: [],
      ));
      return;
    }

    // FIXED: Don't skip changes for self-excluded members
    // Self-excluded proposers can still have field changes that should be shown

    if (oldMember.userId != newMember.userId && newMember.isRemoved != true) {
      memberChanges!.add(ChangedValues(
        changeTitle: 'User ID',
        changeOldValue: oldMember.userId,
        changeNewValue: newMember.userId,
        documentsRequired: [],
      ));
    }
    if (oldMember.insuredId != newMember.insuredId &&
        newMember.isRemoved != true) {
      memberChanges!.add(ChangedValues(
        changeTitle: 'Insured ID',
        changeOldValue: oldMember.insuredId,
        changeNewValue: newMember.insuredId,
        documentsRequired: [],
      ));
    }
    if (oldMember.name != newMember.name && newMember.isRemoved != true) {
      bool confirmationNeeded = false;

      /// can't derive from here
      // if (oldMember.userId.isNotNullOrEmpty ||
      //     newMember.userId.isNotNullOrEmpty) {
      //   confirmationNeeded = true;
      // }

      memberChanges!.add(ChangedValues(
          changeTitle: 'Name change',
          changeOldValue: oldMember.name,
          changeNewValue: newMember.name,
          documentsRequired: [],
          confirmationNeeded: confirmationNeeded));
    }
    if (oldMember.gender != newMember.gender && newMember.isRemoved != true) {
      memberChanges!.add(ChangedValues(
        changeTitle: 'Gender change for $name',
        changeOldValue: oldMember.gender.toString().toSentenceCase(),
        changeNewValue: newMember.gender.toString().toSentenceCase(),
        documentsRequired: [],
      ));
    }
    if (oldMember.dateOfBirth != newMember.dateOfBirth &&
        newMember.isRemoved != true) {
      memberChanges!.add(ChangedValues(
        changeTitle: 'Date of birth change for $name',
        changeOldValue: HealthJourneyManagerUtils()
            .convertHyphenToSlash(oldMember.dateOfBirth ?? ''),
        changeNewValue: HealthJourneyManagerUtils()
            .convertHyphenToSlash(newMember.dateOfBirth ?? ''),
        documentsRequired: [],
      ));
    }
    if (oldMember.height != newMember.height && newMember.isRemoved != true) {
      memberChanges!.add(ChangedValues(
        changeTitle: 'Height change for $name',
        changeOldValue: "${oldMember.height} ft",
        changeNewValue: "${newMember.height} ft",
        documentsRequired: [],
      ));
    }
    if (oldMember.weight != newMember.weight && newMember.isRemoved != true) {
      memberChanges!.add(ChangedValues(
        changeTitle: 'Weight change for $name',
        changeOldValue: "${oldMember.weight} kg",
        changeNewValue: "${newMember.weight} kg",
        documentsRequired: [],
      ));
    }
    if (oldMember.pinCode != newMember.pinCode && newMember.isRemoved != true) {
      memberChanges!.add(ChangedValues(
        changeTitle: 'Pincode change for $name',
        changeOldValue: oldMember.pinCode,
        changeNewValue: newMember.pinCode,
        documentsRequired: [],
      ));
    }
    if (oldMember.mobileNumber != newMember.mobileNumber &&
        newMember.isRemoved != true) {
      memberChanges!.add(ChangedValues(
        changeTitle: 'Mobile number change for $name',
        changeOldValue: oldMember.mobileNumber,
        changeNewValue: newMember.mobileNumber,
        documentsRequired: [],
      ));
    }
    if (oldMember.email != newMember.email && newMember.isRemoved != true) {
      memberChanges!.add(ChangedValues(
        changeTitle: 'Email change for $name',
        changeOldValue: oldMember.email,
        changeNewValue: newMember.email,
        documentsRequired: [],
      ));
    }
    if (oldMember.relation != newMember.relation &&
        newMember.isRemoved != true) {
      memberChanges!.add(ChangedValues(
        changeTitle: 'Relation change for $name',
        changeOldValue: oldMember.relation,
        changeNewValue: newMember.relation,
        documentsRequired: [],
      ));
    }
    // FIXED: For regular member removal, add to updatedMembersList (member addition/removal section)
    // not to memberChanges (member details section) to avoid duplication
    if (oldMember.isRemoved != newMember.isRemoved &&
        newMember.isRemoved == true &&
        !newMember.isSelfExcluded) {
      updatedMembersList!.add(ChangedValues(
        changeTitle: 'Member removed',
        updatedMemberTitle: oldMember.name,
        updatedMemberSubtitle: _formatMemberDetails(oldMember),
        documentsRequired: [],
      ));
    }
    // todo: check if newly added one works
    if (oldMember.isNewlyAdded != newMember.isNewlyAdded &&
        newMember.isNewlyAdded == true) {
      memberChanges!.add(ChangedValues(
        changeTitle: 'Member added',
        updatedMemberTitle: newMember.name,
        updatedMemberSubtitle: _formatMemberDetails(newMember),
        documentsRequired: [],
      ));
    }
  }
}

class ChangedValues {
  final String? changeTitle;
  final String? changeOldValue;
  final String? changeNewValue;
  final String? updatedMemberTitle;
  final String? updatedMemberSubtitle;
  final List<String>? documentsRequired;
  final String? insuredId;
  final String? editId;
  AckoDocumentModel? documents;
  final bool confirmationNeeded;

  /// proposer name change (as of now)

  ChangedValues(
      {this.changeTitle,
      this.changeOldValue,
      this.changeNewValue,
      this.updatedMemberTitle,
      this.updatedMemberSubtitle,
      this.documentsRequired,
      this.insuredId,
      this.editId,
      this.documents,
      this.confirmationNeeded = false});
}
