import 'dart:math';

import 'package:acko_flutter/common/model/BaseModel.dart';
import 'package:acko_flutter/feature/endorsement/acko_doc_upload/model/ppe_doc_upload_response.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_utils.dart';
import 'package:acko_flutter/feature/endorsement/domain/models/force_login_model.dart';
import 'package:acko_flutter/feature/endorsement/domain/models/hl_endorsement_copies.dart'
    as hlEndorsementCopies;
import 'package:acko_flutter/feature/endorsement/domain/repository/hl_endorsement_repository.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_repo.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/domain/models/proposal_details_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/tracking_bloc/ppe_tracking_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_edit_tracking_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_form_editing_models.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/validation_models/family_constrains.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/utils.dart';
import 'package:intl/intl.dart';

class HealthJourneyManagerResponse extends BaseModel {
  String? currentNodeId;
  String? nextNodeId;
  NodeData? edit;
  NodeData? endorsement;
  NodeData? renewal;
  String? inputDataId;
  String? policyNumber;
  String? phoneNumber;
  String? customMessage;
  String? stateUrl;
  String? redirectUrl;
  String? cta;
  bool? authorised;
  bool? newSkuPlan;
  InputData? inputData;

  HealthJourneyManagerResponse({
    this.currentNodeId,
    this.nextNodeId,
    this.edit,
    this.endorsement,
    this.renewal,
    this.inputDataId,
    this.policyNumber,
    this.phoneNumber,
    this.customMessage,
    this.stateUrl,
    this.redirectUrl,
    this.cta,
    this.authorised,
    this.inputData,
    this.newSkuPlan,
  });

  HealthJourneyManagerResponse.error(error) {
    this.error = error;
  }

  HealthJourneyManagerResponse copyWith({
    String? currentNodeId,
    String? nextNodeId,
    NodeData? edit,
    NodeData? endorsement,
    NodeData? renewal,
    String? inputDataId,
    String? policyNumber,
    String? phoneNumber,
    String? customMessage,
    String? stateUrl,
    String? redirectUrl,
    String? cta,
    bool? authorised,
    InputData? inputData,
    bool? newSkuPlan,
  }) {
    return HealthJourneyManagerResponse(
        currentNodeId: currentNodeId ?? this.currentNodeId,
        nextNodeId: nextNodeId ?? this.nextNodeId,
        edit: edit ?? this.edit,
        endorsement: endorsement ?? this.endorsement,
        renewal: renewal ?? this.renewal,
        inputDataId: inputDataId ?? this.inputDataId,
        policyNumber: policyNumber ?? this.policyNumber,
        phoneNumber: phoneNumber ?? this.phoneNumber,
        customMessage: customMessage ?? this.customMessage,
        stateUrl: stateUrl ?? this.stateUrl,
        redirectUrl: redirectUrl ?? this.redirectUrl,
        cta: cta ?? this.cta,
        authorised: authorised ?? this.authorised,
        newSkuPlan: newSkuPlan ?? this.newSkuPlan,
        inputData: inputData ?? this.inputData);
  }

  PrePolicyEditNodes? getHealthJourneyManagerNode(
      {PPETrackingStates? trackingState}) {
    switch (nextNodeId) {
      case 'overview:ppe':
        return PrePolicyEditNodes.OVERVIEW;
      case 'premium:ppe':
        return PrePolicyEditNodes.PREMIUM;
      case 'add_member:ppe':
        return PrePolicyEditNodes.ADD_MEMBER;
      case 'edit:ppe':
        return PrePolicyEditNodes.EDIT;
      case 'hydrate_details:ppe':
        return PrePolicyEditNodes.HYDRATE_DETAILS;
      case 'review:ppe':
        return PrePolicyEditNodes.REVIEW;
      case 'summary:ppe':
        return PrePolicyEditNodes.SUMMARY;
      case 'track:ppe':
        return PrePolicyEditNodes.TRACKING;
      case 'checkout:ppe':
        return PrePolicyEditNodes.CHECKOUT;
      case 'cancel_draft:ppe':
        return PrePolicyEditNodes.CANCEL_DRAFT;
      default:
        return null;
    }
  }

  HealthJourneyManagerResponse.fromJson(Map<String, dynamic> json) {
    currentNodeId = json['current_node_id'];
    nextNodeId = json['next_node_id'];
    inputDataId = json['input_data_id'];
    edit = json['edit'] != null ? new NodeData.fromJson(json['edit']) : null;
    endorsement = json['endorsement'] != null
        ? new NodeData.fromJson(json['endorsement'])
        : null;
    renewal =
        json['renewal'] != null ? new NodeData.fromJson(json['renewal']) : null;
    policyNumber = json['policy_number'];
    phoneNumber = json['phone_number'];
    customMessage = json['custom_message'];
    stateUrl = json['state_url'];
    redirectUrl = json['redirect_url'];
    cta = json['cta'];
    authorised = json['authorised'];
    inputData = json['input_data'] != null
        ? InputData.fromJson(json['input_data'])
        : null;
    newSkuPlan = json['newSkuPlan'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    if (currentNodeId != null) {
      data['current_node_id'] = currentNodeId;
    }

    if (nextNodeId != null) {
      data['next_node_id'] = nextNodeId;
    }

    if (edit != null) {
      data['edit'] = edit!.toJson();
    }

    if (renewal != null) {
      data['renewal'] = renewal!.toJson();
    }

    if (endorsement != null) {
      data['endorsement'] = endorsement!.toJson();
    }

    if (inputDataId != null) {
      data['input_data_id'] = inputDataId;
    }

    if (policyNumber != null) {
      data['policy_number'] = policyNumber;
    }

    if (phoneNumber != null) {
      data['phone_number'] = phoneNumber;
    }

    if (customMessage != null) {
      data['custom_message'] = customMessage;
    }

    if (stateUrl != null) {
      data['state_url'] = stateUrl;
    }

    if (redirectUrl != null) {
      data['redirect_url'] = redirectUrl;
    }

    if (cta != null) {
      data['cta'] = cta;
    }

    return data;
  }

  Map<String, dynamic> toRequestBody(String nodeToCall,
      {bool docUpload = false,
      bool checkout = false,
      InputData? inputData = null}) {
    final Map<String, dynamic> data = {};

    // if (currentNodeId != null) {
    data['current_node_id'] = nodeToCall; // currentNodeId;
    // }

    // data['next_node_id'] = nodeToCall;

    if (nextNodeId != null) {
      data['next_node_id'] = nextNodeId;
    }

    if (edit != null) {
      data['edit'] = edit!.toRequestBody(
          docUpload: docUpload, checkout: checkout, inputData: inputData);
    }

    if (renewal != null) {
      data['renewal'] = renewal!.toRenewalRequestBody();
    }

    if (inputDataId != null) {
      data['input_data_id'] = inputDataId;
    }

    if (policyNumber != null) {
      data['policy_number'] = policyNumber;
    }

    if (phoneNumber != null) {
      data['phone_number'] = phoneNumber;
    }

    if (customMessage != null) {
      data['custom_message'] = customMessage;
    }

    if (stateUrl != null) {
      data['state_url'] = stateUrl;
    }

    if (redirectUrl != null) {
      data['redirect_url'] = redirectUrl;
    }

    if (cta != null) {
      data['cta'] = cta;
    }

    return data;
  }

  updateFromFormValues(FormValues formValues,
      {JourneyType journeyType = JourneyType.PRE_POLICY_EDIT}) {
    PPEFormEditingDetails? newFormValues = formValues.newValues;
    bool hasHeightOrWeightId =
        edit?.oldValue?.insuredContainer?.insuredMap.values.any((insured) {
              return insured.parameters?.parameterMap['height']?.id != null ||
                  insured.parameters?.parameterMap['weight']?.id != null;
            }) ??
            false;
    if (newFormValues != null) {
      if (journeyType == JourneyType.PRE_POLICY_EDIT) {
        edit?.updateFormValues(newFormValues,
            hasIdParameter: hasHeightOrWeightId);
      } else {
        renewal?.updateFormValues(newFormValues, journeyType: journeyType);
      }
    }
  }
}

class NodeData {
  String? entityId;
  String? journey;
  String? entityType;
  String? paymentFrequecy;
  Map<String, String>? deductibles;
  Values? oldValue;
  Values? newValue;
  List<PackagePlan>? packagePlans;

  /// to get sum insured
  List<EditGroup>? editGroups;
  List<EditRequest>? editRequest;
  DeltaPremium? deltaPremium;
  List<Documents>? documents;
  String? draftState;
  String? policyStartDate;
  String? policyEndDate;
  EntityDetails? entityDetails;
  String? redirectUrl;
  String? editGroupId;
  EndorsementFormValidatorConfig? familyConstrains;
  String? cohort;

  /// family constraints - hydrate, overview, edit, add member, premium, cancel draft (not required in review, checkout, summary)
  String? policyNumber;
  String? proposalId;
  String? latestProposalId;
  String? paymentFrequency;
  Map<String, dynamic>? proposal;
  Map<String, dynamic>? declarations;
  CloneProposal? cloneProposal;
  List<Quotes>? quotes;
  String? cloneProposalId;
  List<dynamic>? orders;
  Map<String, dynamic>? paymentPlan;
  Map<String, dynamic>? paymentLink;
  RenewalFilteredEndorsements? filteredEndorsement;
  List<String>? docsRequired;
  List<dynamic>? endorsements;
  Map<String, dynamic>? newPremium;
  List<dynamic>? endorsementRequest;
  List<Documents>? documentsUploaded;
  List<String>? allowedFrequencies;
  String? renewalDiffText;
  bool? postExpiry;
  String? productPlanType;
  String? userPaymentJourneyFlow;
  bool? isMandateEnabled;

  NodeData({
    this.oldValue,
    this.newValue,
    this.entityId,
    this.journey,
    this.entityType,
    this.paymentFrequecy,
    this.deductibles,
    this.packagePlans,
    this.editGroups,
    this.editRequest,
    this.deltaPremium,
    this.documents,
    this.draftState,
    this.policyStartDate,
    this.policyEndDate,
    this.entityDetails,
    this.redirectUrl,
    this.editGroupId,
    this.policyNumber,
    this.proposalId,
    this.latestProposalId,
    this.paymentFrequency,
    this.proposal,
    this.declarations,
    this.cloneProposal,
    this.quotes,
    this.cloneProposalId,
    this.orders,
    this.paymentPlan,
    this.paymentLink,
    this.filteredEndorsement,
    this.docsRequired,
    this.endorsements,
    this.newPremium,
    this.endorsementRequest,
    this.familyConstrains,
    this.documentsUploaded,
    this.allowedFrequencies,
    this.renewalDiffText,
    this.postExpiry,
    this.productPlanType,
    this.userPaymentJourneyFlow,
    this.isMandateEnabled,
    this.cohort,
  });

  NodeData copyWith({
    CloneProposal? cloneProposal,
    List<Quotes>? quotes,
  }) {
    return NodeData(
      cloneProposal: cloneProposal ?? this.cloneProposal,
      quotes: quotes ?? this.quotes,
    );
  }

  factory NodeData.fromJson(Map<String, dynamic> json) {
    return NodeData(
      oldValue:
          json['old_value'] != null ? Values.fromJson(json['old_value']) : null,
      newValue:
          json['new_value'] != null ? Values.fromJson(json['new_value']) : null,
      entityId: json['entity_id'],
      journey: json['journey'],
      entityType: json['entity_type'],
      paymentFrequecy: json['payment_frequecy'],
      deductibles: json['deductibles'] != null
          ? Map<String, String>.from(json['deductibles'])
          : null,
      packagePlans: json['package_plans'] != null
          ? (json['package_plans'] as List<dynamic>?)
              ?.map((item) => PackagePlan.fromJson(item))
              .toList()
          : null,
      editGroups: json['edit_groups'] != null
          ? (json['edit_groups'] as List<dynamic>)
              .map((v) => EditGroup.fromJson(v))
              .toList()
          : null,
      editRequest: json['edit_request'] != null
          ? (json['edit_request'] as List<dynamic>)
              .map((v) => EditRequest.fromJson(v))
              .toList()
          : null,
      deltaPremium: json['delta_premium'] != null
          ? DeltaPremium.fromJson(json['delta_premium'])
          : null,
      documents: json['documents'] != null
          ? (json['documents'] as List<dynamic>)
              .map((v) => Documents.fromJson(v))
              .toList()
          : null,
      draftState: json['draft_status'],
      policyStartDate: json['policy_start_date'],
      policyEndDate: json['policy_end_date'],
      entityDetails: json['entity_details'] != null
          ? EntityDetails.fromJson(json['entity_details'])
          : null,
      redirectUrl: json['redirect_url'],
      editGroupId: json['edit_group_id'],
      policyNumber: json['policy_number'],
      proposalId: json['proposal_id'],
      latestProposalId: json['latest_proposal_id'],
      paymentFrequency: json['payment_frequency'],
      proposal: json['proposal'],
      declarations: json['declarations'],
      cloneProposal: json['clone_proposal'] != null
          ? CloneProposal.fromJson(json['clone_proposal'])
          : null,
      quotes: json['quotes'] != null
          ? (json['quotes'] as List<dynamic>)
              .map((v) => Quotes.fromJson(v))
              .toList()
          : null,
      cloneProposalId: json['clone_proposal_id'],
      orders: json['orders'],
      paymentPlan: json['payment_plan'],
      paymentLink: json['payment_link'],
      filteredEndorsement: json['filtered_endorsement'] != null
          ? RenewalFilteredEndorsements.fromJson(json['filtered_endorsement'])
          : null,
      docsRequired: json['docs_required'] != null
          ? (json['docs_required'] as List<dynamic>)
              .map((e) => e.toString())
              .toList()
          : null,
      endorsements: json['endorsements'],
      newPremium: json['new_premium'],
      endorsementRequest: json['endorsement_request'],
      familyConstrains: json['family_constrains'] != null
          ? EndorsementFormValidatorConfig.fromJson(json['family_constrains'])
          : null,
      documentsUploaded: json['documents_uploaded'] != null
          ? (json['documents_uploaded'] as List<dynamic>)
              .map((v) => Documents.fromJson(v))
              .toList()
          : null,
      allowedFrequencies: json['allowed_frequencies'] != null
          ? (json['allowed_frequencies'] as List<dynamic>)
              .map((e) => e.toString())
              .toList()
          : null,
      renewalDiffText: json['renewal_diff_text'],
      postExpiry: json['post_expiry'],
      productPlanType: json['product_plan_type'],
      userPaymentJourneyFlow: json['user_payment_journey_flow'],
      isMandateEnabled: json['is_mandate_enabled'],
      cohort: json['cohort'],
    );
  }

  Map<String, dynamic> toRequestBody(
      {bool docUpload = false,
      bool checkout = false,
      InputData? inputData = null}) {
    final Map<String, dynamic> data = {};
    if (oldValue != null && !docUpload && !checkout) {
      data['old_value'] = oldValue!.toJson();
    }
    if (newValue != null && !docUpload && !checkout) {
      data['new_value'] = newValue!.toJson();
    }
    if (entityId != null) {
      data['entity_id'] = entityId;
    }
    if (journey != null) {
      data['journey'] = journey;
    }
    if (entityType != null) {
      data['entity_type'] = entityType;
    }
    if (this.documents != null && docUpload && !checkout) {
      data['documents'] = this.documents!.map((v) => v.toJson()).toList();
    }
    if (this.editRequest != null && checkout) {
      data['edit_request'] = this.editRequest!.map((v) => v.toJson()).toList();
    }
    if (inputData?.isMandateEnabled ?? false) {
      data['is_mandate_enabled'] = inputData?.isMandateEnabled;
    }
    if (inputData?.maxMandateAmount != null) {
      data['max_mandate_amount'] = inputData?.maxMandateAmount;
    }
    if (inputData?.mandateMaxYears != null) {
      data['mandate_max_years'] = inputData?.mandateMaxYears;
    }
    if (inputData?.mandateMultiplier != null) {
      data['mandate_multiplier'] = inputData?.mandateMultiplier;
    }
    return data;
  }

  Map<String, dynamic> toRenewalRequestBody() {
    final Map<String, dynamic> data = {};
    if (oldValue != null) {
      data['old_value'] = oldValue!.toJson();
    }
    if (newValue != null) {
      data['new_value'] = newValue!.toJson();
    }
    if (entityId != null) {
      data['entity_id'] = entityId;
    }
    if (journey != null) {
      data['journey'] = journey;
    }
    if (entityType != null) {
      data['entity_type'] = entityType;
    }
    if (paymentFrequecy != null) {
      data['payment_frequecy'] = paymentFrequecy;
    }
    if (deductibles != null) {
      data['deductibles'] = deductibles;
    }
    if (packagePlans != null) {
      data['package_plans'] = packagePlans!.map((v) => v.toJson()).toList();
    }
    if (editGroups != null) {
      data['edit_groups'] = editGroups!.map((v) => v.toJson()).toList();
    }
    if (this.editRequest != null) {
      data['edit_request'] = this.editRequest!.map((v) => v.toJson()).toList();
    }
    if (deltaPremium != null) {
      data['delta_premium'] = deltaPremium!.toJson();
    }
    if (this.documents != null) {
      data['documents'] = this.documents!.map((v) => v.toJson()).toList();
    }
    if (draftState != null) {
      data['draft_status'] = draftState;
    }
    if (policyStartDate != null) {
      data['policy_start_date'] = policyStartDate;
    }
    if (policyEndDate != null) {
      data['policy_end_date'] = policyEndDate;
    }
    if (entityDetails != null) {
      data['entity_details'] = entityDetails!.toJson();
    }
    if (editGroupId != null) {
      data['edit_group_id'] = editGroupId;
    }
    if (familyConstrains != null) {
      data['family_constrains'] = familyConstrains;
    }
    if (policyNumber != null) {
      data['policy_number'] = policyNumber;
    }
    if (proposalId != null) {
      data['proposal_id'] = proposalId;
    }
    if (latestProposalId != null) {
      data['latest_proposal_id'] = latestProposalId;
    }
    if (paymentFrequency != null) {
      data['payment_frequency'] = paymentFrequency;
    }
    if (proposal != null) {
      data['proposal'] = proposal;
    }
    if (declarations != null) {
      data['declarations'] = declarations;
    }
    if (cloneProposal != null) {
      data['clone_proposal'] = cloneProposal!.toJson();
    }
    if (quotes != null) {
      data['quotes'] = this.quotes!.map((e) => e.toJson()).toList();
    }
    if (cloneProposalId != null) {
      data['clone_proposal_id'] = cloneProposalId;
    }
    if (orders != null) {
      data['orders'] = orders;
    }
    if (paymentPlan != null) {
      data['payment_plan'] = paymentPlan;
    }
    if (paymentLink != null) {
      data['payment_link'] = paymentLink;
    }
    if (filteredEndorsement != null) {
      data['filtered_endorsement'] = filteredEndorsement!.toJson();
    }
    if (docsRequired != null) {
      data['docs_required'] = docsRequired;
    }
    if (endorsements != null) {
      data['endorsements'] = endorsements;
    }
    if (newPremium != null) {
      data['new_premium'] = newPremium;
    }
    if (endorsementRequest != null) {
      data['endorsement_request'] = endorsementRequest;
    }
    if (documentsUploaded != null) {
      data['documents_uploaded'] =
          documentsUploaded!.map((v) => v.toJson()).toList();
    }
    if (allowedFrequencies != null) {
      data['allowed_frequencies'] = allowedFrequencies;
    }
    if (renewalDiffText != null) {
      data['renewal_diff_text'] = renewalDiffText;
    }
    if (postExpiry != null) {
      data['post_expiry'] = postExpiry;
    }
    if (productPlanType != null) {
      data['product_plan_type'] = productPlanType;
    }
    if (userPaymentJourneyFlow != null) {
      data['user_payment_journey_flow'] = userPaymentJourneyFlow;
    }
    if (isMandateEnabled != null) {
      data['is_mandate_enabled'] = isMandateEnabled;
    }
    return data;
  }

  /// required policy start date format: dd MM yyyy
  /// Design QA - dd MMM 'yy
  String get formattedPolicyStartDate {
    if (policyStartDate.isNullOrEmpty) return '';
    final dateTime = DateFormat("yyyy-MM-dd").parse(policyStartDate!, false);
    var formattedDate = DateFormat("d MMM yy").format(dateTime.toLocal());
    formattedDate = formattedDate.substring(0, formattedDate.length - 2) +
        "‘" +
        formattedDate.substring(formattedDate.length - 2);
    return formattedDate;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (oldValue != null) {
      data['old_value'] = oldValue!.toJson();
    }
    if (newValue != null) {
      data['new_value'] = newValue!.toJson();
    }
    if (entityId != null) {
      data['entity_id'] = entityId;
    }
    if (journey != null) {
      data['journey'] = journey;
    }
    if (entityType != null) {
      data['entity_type'] = entityType;
    }
    if (paymentFrequecy != null) {
      data['payment_frequecy'] = paymentFrequecy;
    }
    if (deductibles != null) {
      data['deductibles'] = deductibles;
    }
    if (packagePlans != null) {
      data['package_plans'] = packagePlans!.map((v) => v.toJson()).toList();
    }
    if (editGroups != null) {
      data['edit_groups'] = editGroups!.map((v) => v.toJson()).toList();
    }
    if (this.editRequest != null) {
      data['edit_request'] = this.editRequest!.map((v) => v.toJson()).toList();
    }
    if (deltaPremium != null) {
      data['delta_premium'] = deltaPremium!.toJson();
    }
    if (this.documents != null) {
      data['documents'] = this.documents!.map((v) => v.toJson()).toList();
    }
    if (draftState != null) {
      data['draft_status'] = draftState;
    }

    if (policyStartDate != null) {
      data['policy_start_date'] = policyStartDate;
    }
    if (policyEndDate != null) {
      data['policy_end_date'] = policyEndDate;
    }
    if (entityDetails != null) {
      data['entity_details'] = entityDetails!.toJson();
    }

    if (editGroupId != null) {
      data['edit_group_id'] = editGroupId;
    }

    if (familyConstrains != null) {
      data['family_constrains'] = familyConstrains;
    }

    if (policyNumber != null) {
      data['policy_number'] = policyNumber;
    }
    if (proposalId != null) {
      data['proposal_id'] = proposalId;
    }
    if (latestProposalId != null) {
      data['latest_proposal_id'] = latestProposalId;
    }
    if (paymentFrequency != null) {
      data['payment_frequency'] = paymentFrequency;
    }
    if (proposal != null) {
      data['proposal'] = proposal;
    }
    if (declarations != null) {
      data['declarations'] = declarations;
    }
    if (cloneProposal != null) {
      data['clone_proposal'] = cloneProposal!.toJson();
    }
    if (quotes != null) {
      data['quotes'] = this.quotes!.map((e) => e.toJson()).toList();
    }
    if (cloneProposalId != null) {
      data['clone_proposal_id'] = cloneProposalId;
    }
    if (orders != null) {
      data['orders'] = orders;
    }
    if (paymentPlan != null) {
      data['payment_plan'] = paymentPlan;
    }
    if (paymentLink != null) {
      data['payment_link'] = paymentLink;
    }
    if (filteredEndorsement != null) {
      data['filtered_endorsement'] = filteredEndorsement!.toJson();
    }
    if (docsRequired != null) {
      data['docs_required'] = docsRequired;
    }
    if (endorsements != null) {
      data['endorsements'] = endorsements;
    }
    if (newPremium != null) {
      data['new_premium'] = newPremium;
    }
    if (endorsementRequest != null) {
      data['endorsement_request'] = endorsementRequest;
    }
    if (documentsUploaded != null) {
      data['documents_uploaded'] =
          documentsUploaded!.map((v) => v.toJson()).toList();
    }
    if (allowedFrequencies.isNotNullOrEmpty) {
      data['allowed_frequencies'] = allowedFrequencies;
    }
    if (renewalDiffText != null) {
      data['renewal_diff_text'] = renewalDiffText;
    }
    if (postExpiry != null) {
      data['post_expiry'] = postExpiry;
    }
    if (productPlanType != null) {
      data['product_plan_type'] = productPlanType;
    }
    if (userPaymentJourneyFlow != null) {
      data['user_payment_journey_flow'] = userPaymentJourneyFlow;
    }
    if (isMandateEnabled != null) {
      data['is_mandate_enabled'] = isMandateEnabled;
    }
    return data;
  }

  // Map<String, dynamic> toJson() {
  //   final Map<String, dynamic> data = {};
  //   if (oldValue != null) {
  //     data['old_value'] = oldValue!.toJson();
  //   }
  //   if (newValue != null) {
  //     data['new_value'] = newValue!.toJson();
  //   }
  //   return data;
  // }

  List<PrePolicyEditsTrackingModel> generatePrePolicyEditsTrackingModels(
      String? proposalId, PPETrackingStates ppeTrackingState) {
    if (editGroups == null) {
      return [];
    }

    List<PrePolicyEditsTrackingModel> trackingModels = [];

    for (EditGroup editGroup in editGroups!) {
      List<PrePolicyEditDetails> editDetails = [];

      // todo: commenting this logic will remove once tracking will have serparate node
      // if (ppeTrackingState == PPETrackingStates.CHANGES_SUBMITTED &&
      //     proposalId != editGroup.proposalId) continue;

      if (editGroup.edits.isNullOrEmpty) continue;

      for (Edits? edit in editGroup.edits!) {
        if (edit == null) continue;

        if (edit.status.equalsIgnoreCase("cancelled")) continue;

        /// Consider only the first endorsement in the list
        if (edit.endorsements != null && edit.endorsements!.isNotEmpty) {
          Endorsements endorsement = edit.endorsements!.first;
          bool isMemberChange =
              endorsement.endorsementType == 'member_addition' ||
                  endorsement.endorsementType == 'member_removal';

          /// for member addition
          String? insuredId;

          if (endorsement.endorsementType == 'member_addition') {
            insuredId = endorsement.newValue.first['insured_id'];
          } else if (endorsement.endorsementType == 'member_removal') {
            insuredId = endorsement.oldValue[
                'insured_id']; // for old val it isn't coming in list...
          } else
            insuredId = endorsement.parameters?["insured_id"];

          Insured? insuredParameters =
              editGroup.newValue?.insuredContainer?.insuredMap[insuredId];

          String? name =
              insuredParameters?.parameters?.parameterMap['name']?.value;

          String changeTitle =
              _getChangeTitle(endorsement.endorsementType, name);

          /// Determine if the current endorsement is related to member addition/removal
          dynamic oldVal = endorsement.oldValue;
          dynamic newVal = endorsement.newValue;

          if (endorsement.endorsementType == "height_change") {
            oldVal = HealthJourneyManagerUtils()
                .fromAckoHeightToInchFeet(endorsement.oldValue.toString());
            newVal = HealthJourneyManagerUtils()
                .fromAckoHeightToInchFeet(endorsement.newValue.toString());
          } else if (endorsement.endorsementType == "weight_change") {
            oldVal = "${endorsement.oldValue} kg";
            newVal = "${endorsement.newValue} kg";
          } else if (endorsement.endorsementType == "height_change") {
            oldVal = "${endorsement.oldValue} ft";
            newVal = "${endorsement.newValue} ft";
          } else if (endorsement.endorsementType == "gender_change") {
            oldVal = "${endorsement.oldValue}".toSentenceCase();
            newVal = "${endorsement.newValue}".toSentenceCase();
          } else if (endorsement.endorsementType == "sum_insured_change") {
            oldVal = HealthJourneyManagerUtils()
                .formatCurrencyWithUnits(oldVal.toString(), fullUnit: false);
            newVal = HealthJourneyManagerUtils()
                .formatCurrencyWithUnits(newVal.toString(), fullUnit: false);
          } else if (endorsement.endorsementType == "deductible_change") {
            oldVal = HealthJourneyManagerUtils()
                .formatCurrencyWithUnits(oldVal.toString(), fullUnit: false);
            newVal = HealthJourneyManagerUtils()
                .formatCurrencyWithUnits(newVal.toString(), fullUnit: false);
          } else if (endorsement.endorsementType == "porting_date_change") {
            String parsedOldDate = _convertDateFormat(endorsement.oldValue);
            String parsedNewDate = _convertDateFormat(endorsement.newValue);
            oldVal =
                HealthJourneyManagerUtils().convertHyphenToSlash(parsedOldDate);
            newVal =
                HealthJourneyManagerUtils().convertHyphenToSlash(parsedNewDate);
          } else if (endorsement.endorsementType == "dob_change") {
            oldVal = HealthJourneyManagerUtils()
                .convertHyphenToSlash(endorsement.oldValue);
            newVal = HealthJourneyManagerUtils()
                .convertHyphenToSlash(endorsement.newValue);
          } else {
            // oldVal = endorsement.oldValue;
            oldVal = endorsement.oldValue?.toString();
            // newVal = endorsement.newValue;
            newVal = endorsement.newValue?.toString();
          }

          String? redirectionUrl;
          String? redirectionCta;
          String? redirectionCtaText;

          if (edit.actionRequired.isNotNullOrEmpty) {
            redirectionUrl = edit.actionRequired?.firstOrNull?.url ?? null;
            redirectionCta = edit.actionRequired?.firstOrNull?.cta ?? null;
            redirectionCtaText =
                edit.actionRequired?.firstOrNull?.ctaText ?? null;
          }

          // _getRedirectionCtaText();

          editDetails.add(
            PrePolicyEditDetails(
              changeTitle: changeTitle,
              oldValue: !isMemberChange ? oldVal : null,
              newValue: !isMemberChange ? newVal : null,
              status: edit.status,
              reason: _getRemark(edit.remark, edit.status),
              redirectionUrl: redirectionUrl,
              redirectionCtaText: redirectionCtaText,
              redirectionTitle: redirectionCta,
              updatedMemberTitle: isMemberChange
                  ? _getMemberName(
                      endorsement.endorsementType == 'member_addition'
                          ? endorsement.newValue
                          : endorsement.oldValue)
                  : null,
              updatedMemberSubtitle: isMemberChange
                  ? _formatMemberDetails(
                      endorsement.endorsementType == 'member_addition'
                          ? endorsement.newValue
                          : endorsement.oldValue)
                  : null,
            ),
          );
        }
      }

      /// Add a PrePolicyEditsTrackingModel object for the current EditGroup
      trackingModels.add(PrePolicyEditsTrackingModel(
        title: (ppeTrackingState == PPETrackingStates.CHANGES_SUBMITTED)
            ? "Here is the status of your requested changes"
            : editGroup.formattedCreationDate,
        editGroupStatus: editGroup.status,
        editDetails: editDetails,
      ));
    }

    return trackingModels;
  }

  String _convertDateFormat(String date) {
    DateTime parsedDate = DateFormat('yyyy-MM-dd').parse(date);
    return DateFormat('dd-MM-yyyy').format(parsedDate);
  }

  String? _getRemark(String? reason, String? status) {
    hlEndorsementCopies.HealthJmResponse? rcHealthJMResponse;
    rcHealthJMResponse = HlEndorsementRepository()
        .endorsementCopies
        .endorsement
        ?.health
        ?.domain
        ?.models
        ?.healthJmResponse;

    if (status.equalsIgnoreCase("REJECTED"))
      return rcHealthJMResponse?.rejectionReason;
    else if (status.equalsIgnoreCase("PENDING") ||
        status.equalsIgnoreCase("UNDER_REVIEW"))
      return rcHealthJMResponse?.underReviewReason;
    else
      return null;
  }

  String _formatMemberDetails(dynamic memberDetails) {
    if (memberDetails is String) {
      return memberDetails;
    } else if (memberDetails is List && memberDetails.isNotEmpty) {
      var firstMap = memberDetails[0];
      if (firstMap.containsKey('parameters')) {
        var parameters = firstMap['parameters'] as Map<String, dynamic>;

        String? relationship = parameters['relation'] is Map
            ? parameters['relation']['value']?.toString()
            : null;
        String? gender = parameters['gender'] is Map
            ? parameters['gender']['value']?.toString().toSentenceCase()
            : null;
        String? dob = parameters['dob'] is Map
            ? parameters['dob']['value']?.toString()
            : null;

        return '($relationship, $gender, ${HealthJourneyManagerUtils().convertHyphenToSlash(dob)})';
      }
    } else if (memberDetails is Map) {
      var firstMap = memberDetails;
      if (firstMap.containsKey('parameters')) {
        var parameters = firstMap['parameters'] as Map<String, dynamic>;

        String? relationship = parameters['relation'] is Map
            ? parameters['relation']['value']?.toString()
            : null;
        String? gender = parameters['gender'] is Map
            ? parameters['gender']['value']?.toString().toSentenceCase()
            : null;
        String? dob = parameters['dob'] is Map
            ? parameters['dob']['value']?.toString()
            : null;

        return '($relationship, $gender, ${HealthJourneyManagerUtils().convertHyphenToSlash(dob)})';
      }
    } else if (memberDetails is MemberDetails) {
      String relation = memberDetails.relation ?? '';
      String gender = memberDetails.gender ?? '';
      String dob = memberDetails.dateOfBirth ?? '';
      return '($relation, $gender, ${HealthJourneyManagerUtils().convertHyphenToSlash(dob)})';
    }
    return '';
  }

  _getRedirectionCtaText(String? title) {
    if (title.isNullOrEmpty) return "";
    if (title.containsIgnoreCase("kyc")) {
      return "Start now";
    } else if (title.containsIgnoreCase("health evaluation")) {
      return "View details";
    }
  }

  String _getMemberName(dynamic memberDetails) {
    if (memberDetails is String) {
      return memberDetails;
    } else if (memberDetails is List && memberDetails.isNotEmpty) {
      var firstMap = memberDetails[0];
      if (firstMap.containsKey('parameters')) {
        var parameters = firstMap['parameters'] as Map<String, dynamic>;

        var nameParam = parameters['name'];
        String? name = nameParam['value'];

        return name?.isNotEmpty == true ? name! : '';
      }
    } else if (memberDetails is Map) {
      var firstMap = memberDetails;
      if (firstMap.containsKey('parameters')) {
        var parameters = firstMap['parameters'] as Map<String, dynamic>;

        var nameParam = parameters['name'];
        String? name = nameParam['value'];

        return name?.isNotEmpty == true ? name! : '';
      }
    }
    return '';
  }

  /// Function to generate the title based on endorsement type
  String _getChangeTitle(String? endorsementType, String? name) {
    String nameSuffix = (name != null && name.isNotEmpty) ? ' for $name' : '';

    switch (endorsementType) {
      case 'gender_change':
        return 'Gender change$nameSuffix';
      case 'name_change':
        return 'Name change';
      case 'dob_change':
        return 'Date of birth change$nameSuffix';
      case 'relation_change':
        return 'Relation change$nameSuffix';
      case 'address_change':
        return 'Address change$nameSuffix';
      case 'email_change':
        return 'Email change$nameSuffix';
      case 'mobile_number_change':
        return 'Mobile number change$nameSuffix';
      case 'pincode_change':
        return 'Pin code change'; // $nameSuffix
      case 'height_change':
        return 'Height change$nameSuffix';
      case 'weight_change':
        return 'Weight change$nameSuffix';
      case 'sum_insured_change':
        return 'Sum insured change';
      case 'deductible_change':
        return 'Revised deductible'; // $nameSuffix
      case 'plan_name_change':
        return 'Plan name change'; // $nameSuffix
      case 'porting_date_change':
        return 'Previous policy end date'; // $nameSuffix
      case 'member_addition':
        return 'New member addition'; // $nameSuffix
      case 'member_removal':
        return 'Member removed'; // $nameSuffix
      default:
        return '${endorsementType?.replaceAll('_', ' ') ?? 'change'}$nameSuffix';
    }
  }

  updateFormValues(PPEFormEditingDetails newFormValues,
      {JourneyType journeyType = JourneyType.PRE_POLICY_EDIT,
      bool hasIdParameter = false}) {
    if (newValue != null) {
      newValue?.updateFormValues(newFormValues,
          journeyType: journeyType, hasIdParameter: hasIdParameter);
    }
  }

  int get getTotalPendingEdits {
    if (editRequest.isNullOrEmpty) {
      return 0;
    }

    int count = 0;

    for (var edit in editRequest!) {
      if (edit.endorsements != null && edit.endorsements!.isNotEmpty) {
        var firstEndorsement = edit.endorsements!.first;
        // String editStatus = edit.status;
        // if (editStatus.equalsIgnoreCase('PENDING')) {
        count++;
        // }
      }
    }

    return count;
  }

  int get getRenewalPendingEdits {
    int count = 0;
    if (filteredEndorsement != null) {
      if (filteredEndorsement!.memberRemoval.isNotNullOrEmpty) {
        count += filteredEndorsement!.memberRemoval!.length;
      }
      if (filteredEndorsement!.personalInfo.isNotNullOrEmpty) {
        count += filteredEndorsement!.personalInfo!.length;
      }
      if (filteredEndorsement!.familyMemberInfo.isNotNullOrEmpty) {
        count += filteredEndorsement!.familyMemberInfo!.length;
      }
      if (filteredEndorsement!.memberAddition.isNotNullOrEmpty) {
        count += filteredEndorsement!.memberAddition!.length;
      }
    }
    return count;
  }

  /// this was using edit group
  // int get getTotalPendingEdits {
  //   if (editGroups.isNullOrEmpty) {
  //     return 0;
  //   }
  //
  //   int count = 0;
  //
  //   for (var editGroup in editGroups!) {
  //     String? editGroupStatus = editGroup.status;
  //     if (editGroupStatus.isNullOrEmpty ||
  //         !(editGroupStatus.containsIgnoreCase("PENDING"))) continue;
  //     for (var edit in editGroup.edits ?? []) {
  //       if (edit.endorsements != null && edit.endorsements!.isNotEmpty) {
  //         var firstEndorsement = edit.endorsements!.first;
  //         String editStatus = edit.status;
  //         if (editStatus.equalsIgnoreCase('PENDING')) {
  //           count++;
  //         }
  //       }
  //     }
  //   }
  //
  //   return count;
  // }
}

class Values {
  InsuredContainer? insuredContainer;
  PlansContainer? plansContainer;
  UsersContainer? usersContainer;
  CurrentPlan? currentPlan;
  PolicyPortingDetails? portingDetails;

  Values({
    this.insuredContainer,
    this.plansContainer,
    this.usersContainer,
    this.currentPlan,
    this.portingDetails,
  });

  factory Values.fromJson(Map<String, dynamic> json) {
    return Values(
      insuredContainer: json['insured'] != null
          ? InsuredContainer.fromJson(json['insured'])
          : null,
      plansContainer:
          json['plans'] != null ? PlansContainer.fromJson(json['plans']) : null,
      usersContainer:
          json['users'] != null ? UsersContainer.fromJson(json['users']) : null,
      currentPlan: json['current_plan'] != null
          ? CurrentPlan.fromJson(json['current_plan'])
          : null,
      portingDetails: json['porting_details'] != null
          ? PolicyPortingDetails.fromJson(json['porting_details'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (insuredContainer != null) {
      data['insured'] = insuredContainer!.toJson();
    }
    if (plansContainer != null) {
      data['plans'] = plansContainer!.toJson();
    }
    if (usersContainer != null) {
      data['users'] = usersContainer!.toJson();
    }
    if (this.currentPlan != null) {
      data['current_plan'] = this.currentPlan!.toJson();
    }
    if (this.portingDetails != null) {
      data['porting_details'] = this.portingDetails!.toJson();
    }
    return data;
  }

  updateFormValues(PPEFormEditingDetails newFormValues,
      {JourneyType journeyType = JourneyType.PRE_POLICY_EDIT,
      bool hasIdParameter = false}) {
    if (newFormValues.portingDetails?.portingDate.isNotNullOrEmpty ?? false) {
      portingDetails?.updatePortingDetails(newFormValues.portingDetails!);
    }

    if (newFormValues.policyDetails != null) {
      currentPlan?.updateCurrentPlanDetails(newFormValues.policyDetails!);
    }

    if (journeyType == JourneyType.PRE_POLICY_EDIT &&
        newFormValues.proposerDetails != null) {
      usersContainer?.updateUsersDetails(newFormValues.proposerDetails!);
    }

    if (newFormValues.memberDetails != null ||
        newFormValues.proposerDetails != null) {
      insuredContainer?.updateInsuredDetails(
          newFormValues.memberDetails, newFormValues.proposerDetails,
          journeyType: journeyType, hasIdParameter: hasIdParameter);
    }
  }
}

class InsuredContainer {
  String? index;
  final Map<String, Insured> insuredMap;

  InsuredContainer({required this.index, required this.insuredMap});

  factory InsuredContainer.fromJson(Map<String, dynamic> json) {
    String index = json['index'];
    Map<String, Insured> insuredMap = {};
    json.forEach((key, value) {
      if (key != 'index') {
        insuredMap[key] = Insured.fromJson(value);
      }
    });
    return InsuredContainer(index: index, insuredMap: insuredMap);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    data['index'] = index;

    if (insuredMap.isNotEmpty) {
      insuredMap.forEach((key, value) {
        data[key] = value.toJson();
      });
    }

    return data;
  }

  /// Function to add a new insured member
  void addInsuredMember(
      {required String name,
      required String relationship,
      required String dob,
      required String gender,
      required int height,
      required int weight,
      JourneyType journeyType = JourneyType.PRE_POLICY_EDIT}) {
    final newInsuredId = journeyType == JourneyType.PRE_POLICY_EDIT
        ? _generateRandomId()
        : _generateInsuredIdForRenewal();

    final parameters = Parameters(parameterMap: {
      'id':
          ParameterValue(id: null, value: newInsuredId, parameterVersion: null),
      'name': ParameterValue(id: null, value: name, parameterVersion: null),
      // 'relationship': ParameterValue(id: null, value: relationship, parameterVersion: null), /// relation + gender
      'relation':
          ParameterValue(id: null, value: relationship, parameterVersion: null),
      'gender': ParameterValue(
          id: null, value: gender.toLowerCase(), parameterVersion: null),
      'dob': ParameterValue(id: null, value: dob, parameterVersion: null),
      'height': ParameterValue(id: null, value: height, parameterVersion: null),
      'weight': ParameterValue(id: null, value: weight, parameterVersion: null),
    });

    final newInsured = Insured(
      insuredId: newInsuredId,
      insuredNumber: null,
      parameters: parameters,
    );

    insuredMap[newInsuredId] = newInsured;
  }

  /// Helper function to generate a random alphanumeric ID
  String _generateRandomId() {
    String nowBase36 = DateTime.now().millisecondsSinceEpoch.toRadixString(36);
    String randomBase36 = Random().nextDouble().toString().substring(2);
    return nowBase36 + randomBase36;
  }

  String _generateInsuredIdForRenewal() {
    int now = DateTime.now().millisecondsSinceEpoch;
    String nowBase36 = now.toRadixString(36);
    String randomBase36 = Random().nextDouble().toString().substring(2);
    return nowBase36 + randomBase36;
  }

  void updateUsersDetails(
      MemberDetails proposerDetails, String? insuredId, bool isRenewal) {
    if (proposerDetails.insuredId.isNotNullOrEmpty) {
      if (insuredMap.containsKey(insuredId)) {
        Insured existingUser = insuredMap[proposerDetails.insuredId]!;
        Map<String, dynamic> proposerMap = proposerDetails.toMap();
        existingUser.parameters?.parameterMap.forEach((key, value) {
          if (proposerMap.containsKey(key)) {
            if (value.value != proposerMap[key]) {
              if (key == "height") {
                value.value = int.tryParse(HealthJourneyManagerUtils()
                    .fromInchFeetToAckoHeight(proposerMap[key] ?? ""));
              } else if (key == "weight") {
                value.value = int.tryParse(proposerMap[key] ?? "");
              } else if (isRenewal && key == "email") {
                value.value = proposerMap[key] ?? "";
              } else if (key == "pincode" || key == "phone" || key == "email") {
                /// ignore pincode, phone & email update for proposer
              } else if (key == "role" &&
                  (proposerMap[key] as String?).isNotNullOrEmpty) {
                value.value = "insured";
              }

              /// gender format
              // else if(key == "gender") {
              //   value.value = proposerMap[key]!.toSentenceCase();
              // }
              else
                value.value = proposerMap[key];
            }
          }
        });
        insuredMap[proposerDetails.insuredId!] = existingUser;
      }
    }
  }

  void updateInsuredDetails(
      List<MemberDetails?>? memberDetailsList, MemberDetails? proposerDetails,
      {JourneyType journeyType = JourneyType.PRE_POLICY_EDIT,
      bool hasIdParameter = false}) {
    Set<String> updatedInsuredIds = {};

    // CRITICAL: Always preserve proposer in updatedInsuredIds to prevent removal
    // This ensures proposer is never removed when removing regular members
    if (proposerDetails != null && proposerDetails.insuredId.isNotNullOrEmpty) {
      // Only add proposer to updatedInsuredIds if they're NOT self-excluded
      // Self-excluded proposers should be removed from insuredContainer
      if (!proposerDetails.isSelfExcluded) {
        updatedInsuredIds.add(proposerDetails.insuredId!);
      }
    }

    // CRITICAL FIX: Handle proposer with empty/null insuredId
    // If proposer doesn't have insuredId but exists in insuredMap, find and preserve them
    if (proposerDetails != null &&
        (proposerDetails.insuredId.isNullOrEmpty ||
            !updatedInsuredIds.contains(proposerDetails.insuredId ?? "")) &&
        !proposerDetails.isSelfExcluded) {
      // Find proposer in existing insuredMap by user_id
      String? proposerUserId = proposerDetails.userId;
      if (proposerUserId.isNotNullOrEmpty) {
        for (var entry in insuredMap.entries) {
          String insuredId = entry.key;
          Insured insured = entry.value;
          String? insuredUserId =
              insured.parameters?.parameterMap['user_id']?.value;

          if (insuredUserId.isNotNullOrEmpty &&
              insuredUserId == proposerUserId) {
            // Found proposer in insuredMap - preserve them
            updatedInsuredIds.add(insuredId);

            break;
          }
        }
      }
    }

    // ADDITIONAL SAFEGUARD: If proposer exists in current insuredMap but not in updatedInsuredIds,
    // and they're not explicitly self-excluded, preserve them to prevent accidental removal
    if (proposerDetails != null && proposerDetails.insuredId.isNotNullOrEmpty) {
      String proposerInsuredId = proposerDetails.insuredId!;
      if (insuredMap.containsKey(proposerInsuredId) &&
          !updatedInsuredIds.contains(proposerInsuredId) &&
          !proposerDetails.isSelfExcluded) {
        // Proposer exists in current map but not in updated list - preserve them
        updatedInsuredIds.add(proposerInsuredId);
      }
    }

    if (memberDetailsList == null || memberDetailsList.isEmpty) {
      return;
    }

    /// for tracking of updated insured IDs
    for (var memberDetails in memberDetailsList) {
      if (memberDetails != null) {
        if (memberDetails.insuredId.isNotNullOrEmpty) {
          if (memberDetails.isRemoved) {
            continue;
          }

          updatedInsuredIds.add(memberDetails.insuredId!);

          if (insuredMap.containsKey(memberDetails.insuredId)) {
            Insured existingInsured = insuredMap[memberDetails.insuredId]!;

            Map<String, dynamic> memberMap = memberDetails.toMap();
            for (String key in existingInsured.parameters!.parameterMap.keys) {
              ParameterValue? value =
                  existingInsured.parameters!.parameterMap[key];

              if (memberMap.containsKey(key)) {
                /// Only update the value if it has changed
                if (value?.value != memberMap[key]) {
                  if (key == "height") {
                    value?.value = int.tryParse(HealthJourneyManagerUtils()
                        .fromInchFeetToAckoHeight(memberMap[key] ?? ""));
                  } else if (key == "weight") {
                    value?.value = int.tryParse(memberMap[key] ?? "");
                  } else if (key == "pincode") {
                    // don't update pincode for insured.
                    continue;
                  } else if (key == "role" &&
                      (memberMap[key] as String?).isNotNullOrEmpty) {
                    value?.value = "insured";
                  }

                  /// gender casing
                  // else if(key == "gender") {
                  //   value?.value = memberMap[key]!.toSentenceCase();
                  // }
                  else
                    value?.value = memberMap[key];
                }
              }
            }

            insuredMap[memberDetails.insuredId!] = existingInsured;
          } else {
            /// new member???
            Map<String, ParameterValue> parameterMap = {};
            Map<String, dynamic> memberMap = memberDetails.toMap();
            memberMap.forEach((key, value) {
              if (value != null && !key.contains("insuredNumber")) {
                if (key.contains("height")) {
                  parameterMap[key] = ParameterValue(
                      id: hasIdParameter ? "height" : null,
                      value: int.tryParse(HealthJourneyManagerUtils()
                              .fromInchFeetToAckoHeight(value)) ??
                          "",
                      parameterVersion:
                          memberDetails.insuredNumber.isNotNullOrEmpty
                              ? 1
                              : null);
                } else if (key.contains("weight")) {
                  parameterMap[key] = ParameterValue(
                      id: hasIdParameter ? "weight" : null,
                      value: int.tryParse(value),
                      parameterVersion:
                          memberDetails.insuredNumber.isNotNullOrEmpty
                              ? 1
                              : null);
                } else {
                  parameterMap[key] = ParameterValue(
                      id: null,
                      value: value,
                      parameterVersion:
                          memberDetails.insuredNumber.isNotNullOrEmpty
                              ? 1
                              : null);
                }
              }
            });

            if (journeyType == JourneyType.PRE_POLICY_EDIT &&
                memberDetails.insuredNumber.isNullOrEmpty) {
              parameterMap['journey'] = ParameterValue(
                  id: null, value: 'pre_policy_edit', parameterVersion: null);
            }

            insuredMap[memberDetails.insuredId!] = Insured(
              insuredId: memberDetails.insuredId!,
              insuredNumber: memberDetails.insuredNumber,
              parameters: Parameters(parameterMap: parameterMap),
            );
          }
        }
      }
    }

    /// Delete insured members who are not in the updated list
    insuredMap.removeWhere((key, value) => !updatedInsuredIds.contains(key));
  }
}

class Insured {
  String? insuredId;
  String? insuredNumber;
  Parameters? parameters;

  Insured({this.insuredId, this.parameters, this.insuredNumber});

  factory Insured.fromJson(Map<String, dynamic> json) {
    return Insured(
      insuredId: json['insured_id'],
      insuredNumber: json['insured_number'],
      parameters: json['parameters'] != null
          ? Parameters.fromJson(json['parameters'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    if (insuredId != null) {
      data['insured_id'] = insuredId;
    }

    if (insuredNumber != null) {
      data['insured_number'] = insuredNumber;
    }

    if (parameters != null) {
      data['parameters'] = parameters!.toJson();
    }

    return data;
  }
}

class PlansContainer {
  final String index;
  final Map<String, Plan> plansMap;

  PlansContainer({required this.index, required this.plansMap});

  factory PlansContainer.fromJson(Map<String, dynamic> json) {
    String index = json['index'];
    Map<String, Plan> plansMap = {};
    json.forEach((key, value) {
      if (key != 'index') {
        plansMap[key] = Plan.fromJson(value);
      }
    });
    return PlansContainer(index: index, plansMap: plansMap);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    data['index'] = index;

    if (plansMap.isNotEmpty) {
      plansMap.forEach((key, value) {
        data[key] = value.toJson();
      });
    }

    return data;
  }
}

class Plan {
  Parameters? parameters;
  List<InsuredMapping>? insuredMapping;
  String? sellablePlanUnitId;

  Plan({
    this.parameters,
    this.insuredMapping,
    this.sellablePlanUnitId,
  });

  factory Plan.fromJson(Map<String, dynamic> json) {
    var insuredMappingJson = json['insured_mapping'] as List?;
    List<InsuredMapping>? insuredMappingList =
        insuredMappingJson?.map((i) => InsuredMapping.fromJson(i)).toList();

    return Plan(
      parameters: json['parameters'] != null
          ? Parameters.fromJson(json['parameters'])
          : null,
      insuredMapping: insuredMappingList,
      sellablePlanUnitId: json['sellable_plan_unit_id'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['parameters'] = parameters?.toJson();
    data['insured_mapping'] = insuredMapping?.map((i) => i.toJson()).toList();
    data['sellable_plan_unit_id'] = sellablePlanUnitId;

    return data;
  }
}

class InsuredMapping {
  final String? insuredNumber;
  final String? insuredId;
  final Parameters? parameters;

  InsuredMapping({
    this.insuredNumber,
    this.insuredId,
    this.parameters,
  });

  factory InsuredMapping.fromJson(Map<String, dynamic> json) {
    return InsuredMapping(
      insuredNumber: json['insured_number'],
      insuredId: json['insured_id'],
      parameters: json['parameters'] != null
          ? Parameters.fromJson(json['parameters'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'insured_number': insuredNumber,
      'insured_id': insuredId,
      'parameters': parameters?.toJson(),
    };
  }
}

class UsersContainer {
  final String index;
  final Map<String, User> usersMap;

  UsersContainer({required this.index, required this.usersMap});

  factory UsersContainer.fromJson(Map<String, dynamic> json) {
    String index = json['index'];
    Map<String, User> usersMap = {};
    json.forEach((key, value) {
      if (key != 'index') {
        usersMap[key] = User.fromJson(value);
      }
    });
    return UsersContainer(index: index, usersMap: usersMap);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['index'] = index;
    usersMap.forEach((key, value) {
      data[key] = value.toJson();
    });
    return data;
  }

  void updateUsersDetails(MemberDetails proposerDetails) {
    if (proposerDetails.userId.isNotNullOrEmpty) {
      User? existingUser;
      String? userKey;

      // Try to find user by userId first
      if (usersMap.containsKey(proposerDetails.userId)) {
        existingUser = usersMap[proposerDetails.userId];
        userKey = proposerDetails.userId;
      } else {
        // Fallback: find user by matching user_id parameter in the first user
        // This handles cases where the key might be different
        var firstUserEntry = usersMap.entries.firstOrNull;
        if (firstUserEntry != null) {
          var userIdParam =
              firstUserEntry.value.parameters.parameterMap['user_id']?.value;
          if (userIdParam == proposerDetails.userId) {
            existingUser = firstUserEntry.value;
            userKey = firstUserEntry.key;
          }
        }
      }

      if (existingUser != null && userKey != null) {
        Map<String, dynamic> proposerMap = proposerDetails.toMap();

        existingUser.parameters.parameterMap.forEach((key, value) {
          if (proposerMap.containsKey(key)) {
            if (value.value != proposerMap[key]) {
              // if(key != "id")
              // value.value = proposerMap[key];
              if (key == "height") {
                value.value = int.tryParse(HealthJourneyManagerUtils()
                    .fromInchFeetToAckoHeight(proposerMap[key] ?? ""));
              } else if (key == "weight") {
                value.value = int.tryParse(proposerMap[key] ?? "");
              } else if (key == "gender") {
                /// gender casing (according to buy)
                value.value = proposerMap[key]?.toString().toSentenceCase();
              } else {
                if (key != "id") // "id" is not editable
                  value.value = proposerMap[key];
              }
            }
          }
        });

        usersMap[userKey] = existingUser;
      }
    }
  }
}

class User {
  final String role;
  final Parameters parameters;
  final String userId;

  User({
    required this.role,
    required this.parameters,
    required this.userId,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      role: json['role'],
      parameters: Parameters.fromJson(json['parameters']),
      userId: json['user_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'role': role,
      'parameters': parameters.toJson(),
      'user_id': userId,
    };
  }
}

class Eligibility {
  final String id;
  final dynamic value;

  Eligibility({required this.id, this.value});

  factory Eligibility.fromJson(Map<String, dynamic> json) {
    return Eligibility(
      id: json['id'],
      value: json.containsKey('value') ? json['value'] : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'id': id,
    };
    if (value != null) {
      data['value'] = value;
    }
    return data;
  }
}

class Parameters {
  final Map<String, ParameterValue> parameterMap;

  Parameters({required this.parameterMap});

  factory Parameters.fromJson(Map<String, dynamic> json) {
    Map<String, ParameterValue> parameterMap = {};
    json.forEach((key, value) {
      parameterMap[key] = ParameterValue.fromJson(value);
    });
    return Parameters(parameterMap: parameterMap);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    parameterMap.forEach((key, value) {
      data[key] = value.toJson();
    });
    return data;
  }
}

class ParameterValue {
  dynamic id;
  dynamic value;
  dynamic parameterVersion;
  dynamic uom;

  ParameterValue(
      {required this.id,
      required this.value,
      required this.parameterVersion,
      this.uom});

  factory ParameterValue.fromJson(Map<String, dynamic> json) {
    return ParameterValue(
        id: json['id'],
        value: json['value'],
        parameterVersion: json['parameter_version'],
        uom: json['uom']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (id != null) {
      data['id'] = id;
    }
    if (value != null) {
      data['value'] = value;
    }
    if (parameterVersion != null) {
      data['parameter_version'] = parameterVersion;
    }
    if (uom != null) {
      data['uom'] = uom;
    }
    return data;
  }
}

class EntityDetails {
  String? stage;
  String? status;
  String? entityName;
  String? editReferenceId;
  String? proposalType;
  bool? isEditAllowed;
  String? paymentDate;
  bool? basbaProposal;
  bool? isActiveMandate;
  MandateDetails? mandateDetails;

  EntityDetails({this.stage, this.status});

  EntityDetails.fromJson(Map<String, dynamic> json) {
    stage = json['stage'];
    status = json['status'];
    entityName = json['entity_name'];
    editReferenceId = json['edit_reference_id'];
    proposalType = json['proposal_type'];
    isEditAllowed = json['is_edit_allowed'];
    paymentDate = json['payment_date'];
    basbaProposal = json['basba_proposal'];
    isActiveMandate = json['is_active_mandate'];
    mandateDetails = json['mandate_details'] != null
        ? MandateDetails.fromJson(json['mandate_details'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['stage'] = this.stage;
    data['status'] = this.status;
    data['entity_name'] = this.entityName;
    data['edit_reference_id'] = this.editReferenceId;
    data['proposal_type'] = this.proposalType;
    data['is_edit_allowed'] = this.isEditAllowed;
    data['payment_date'] = this.paymentDate;
    data['basba_proposal'] = this.basbaProposal;
    data['is_active_mandate'] = this.isActiveMandate;
    data['mandate_details'] = this.mandateDetails?.toJson();
    return data;
  }

  EditPolicyType get getEditPolicyType {
    if (proposalType == null) return EditPolicyType.PAYMENT;
    String statusStr = proposalType!.toLowerCase();
    switch (statusStr) {
      case "fresh":
        return EditPolicyType.PAYMENT;
      case "portability":
        return EditPolicyType.PORTING;
      case "renewal":
        return EditPolicyType.RENEWAL;
      default:
        return EditPolicyType.PAYMENT;
    }
  }
}

class MandateDetails {
  String? checkoutNote;
  String? draftNote;
  double? oldMandate;
  double? newMandate;
  String? draftDays;

  MandateDetails(
      {this.checkoutNote,
      this.draftNote,
      this.oldMandate,
      this.newMandate,
      this.draftDays});

  MandateDetails.fromJson(Map<String, dynamic> json) {
    checkoutNote = json['checkout_note'];
    draftNote = json['draft_note'];
    oldMandate = json['old_mandate'] != null
        ? double.tryParse(json['old_mandate'].toString())
        : null;
    newMandate = json['new_mandate'] != null
        ? double.tryParse(json['new_mandate'].toString())
        : null;
    draftDays = json['draft_days'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['checkout_note'] = this.checkoutNote;
    data['draft_note'] = this.draftNote;
    data['old_mandate'] = this.oldMandate;
    data['new_mandate'] = this.newMandate;
    data['draft_days'] = this.draftDays;
    return data;
  }

  String? get formattedOldMandate {
    return oldMandate != null
        ? '₹${NumberUtils.commaSeparatedNumber(oldMandate!.toInt())}'
        : null;
  }

  String? get formattedNewMandate {
    return newMandate != null
        ? '₹${NumberUtils.commaSeparatedNumber(newMandate!.toInt())}'
        : null;
  }
}

class CurrentPlan {
  String? packageId;
  String? packageName;
  String? packageType;
  String? productPlanType;
  String? productId;
  int? sumInsured;
  String? deductible;

  CurrentPlan(
      {this.packageId,
      this.packageName,
      this.packageType,
      this.productPlanType,
      this.productId,
      this.sumInsured,
      this.deductible});

  CurrentPlan.fromJson(Map<String, dynamic> json) {
    packageId = json['package_id'];
    packageName = json['package_name'];
    packageType = json['package_type'];
    productPlanType = json['product_plan_type'];
    productId = json['product_id'];
    sumInsured = json['sum_insured'];
    deductible = json['deductible'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (packageId != null) data['package_id'] = this.packageId;
    if (packageName != null) data['package_name'] = this.packageName;
    if (packageType != null) data['package_type'] = this.packageType;
    if (productPlanType != null)
      data['product_plan_type'] = this.productPlanType;
    if (productId != null) data['product_id'] = this.productId;
    if (sumInsured != null) data['sum_insured'] = this.sumInsured;
    if (deductible != null) data['deductible'] = this.deductible;
    return data;
  }

  void updateCurrentPlanDetails(PolicyDetails policyDetails) {
    /// Check and update packageName ( we don't allow plan name change from App)
    // if (policyDetails.packageName != null && policyDetails.packageName != this.packageName) {
    //   this.packageName = policyDetails.packageName;
    // }

    if (policyDetails.sumInsured != null &&
        policyDetails.sumInsured?.id != this.sumInsured) {
      this.sumInsured = int.tryParse(policyDetails.sumInsured?.id);
    }

    if (policyDetails.deductible != null &&
        policyDetails.deductible?.id != this.deductible) {
      this.deductible = policyDetails.deductible?.id;
    }
  }
}

class PolicyPortingDetails {
  String? currentPolicyExpiryDate;

  PolicyPortingDetails({this.currentPolicyExpiryDate});

  PolicyPortingDetails.fromJson(Map<String, dynamic> json) {
    currentPolicyExpiryDate = json['current_policy_expiry_date'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['current_policy_expiry_date'] = this.currentPolicyExpiryDate;
    return data;
  }

  updatePortingDetails(PortingDetails portingDetails) {
    if (portingDetails.portingDate.isNotNullOrEmpty) {
      currentPolicyExpiryDate = portingDetails.getPortingDate();
    }
  }
}

class PackagePlan {
  String? sumInsuredType;
  String? productType;
  String? productPlanType;
  List<SumInsured>? sumInsureds;
  String? productId;
  String? packageName;
  String? sellablePlanGroupId;
  String? packageId;
  String? packageType;

  PackagePlan({
    this.sumInsuredType,
    this.productType,
    this.productPlanType,
    this.sumInsureds,
    this.productId,
    this.packageName,
    this.sellablePlanGroupId,
    this.packageId,
    this.packageType,
  });

  factory PackagePlan.fromJson(Map<String, dynamic> json) {
    return PackagePlan(
      sumInsuredType: json['sum_insured_type'],
      productType: json['product_type'],
      productPlanType: json['product_plan_type'],
      sumInsureds: (json['sum_insureds'] as List<dynamic>?)
          ?.map((item) => SumInsured.fromJson(item))
          .toList(),
      productId: json['product_id'],
      packageName: json['package_name'],
      sellablePlanGroupId: json['sellable_plan_group_id'],
      packageId: json['package_id'],
      packageType: json['package_type'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (sumInsuredType != null) data['sum_insured_type'] = sumInsuredType;
    if (productType != null) data['product_type'] = productType;
    if (productPlanType != null) data['product_plan_type'] = productPlanType;
    if (sumInsureds != null)
      data['sum_insureds'] = sumInsureds?.map((item) => item.toJson()).toList();
    if (productId != null) data['product_id'] = productId;
    if (packageName != null) data['package_name'] = packageName;
    if (sellablePlanGroupId != null)
      data['sellable_plan_group_id'] = sellablePlanGroupId;
    if (packageId != null) data['package_id'] = packageId;
    if (packageType != null) data['package_type'] = packageType;
    return data;
  }
}

class SumInsured {
  int? value;
  String? displayValue;

  SumInsured({
    this.value,
    this.displayValue,
  });

  factory SumInsured.fromJson(Map<String, dynamic> json) {
    return SumInsured(
      value: json['value'],
      displayValue: json['display_value'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['value'] = value;
    data['display_value'] = displayValue;
    return data;
  }
}

class DeltaPremium {
  double? pendingPremium;
  double? paidAmount;
  double? previousGrossPremium;
  double? newGrossPremium;
  double? previousInstallment;
  double? newInstallment;
  double? adhocPayment;
  double? deltaGrossPremium;
  String? paymentFrequency;
  int? premiumPendingCount;

  DeltaPremium({
    this.pendingPremium,
    this.paidAmount,
    this.previousGrossPremium,
    this.newGrossPremium,
    this.previousInstallment,
    this.newInstallment,
    this.adhocPayment,
    this.paymentFrequency,
    this.premiumPendingCount,
    this.deltaGrossPremium,
  });

  // premiumPendingCount todo: for 3 months count pening_premium_count.

  // private BigDecimal oldInstalmentAmount;
  // private BigDecimal newInstalmentAmount;
  // private BigDecimal premiumDue;
  // private BigDecimal paidPremium;
  // private BigDecimal pendingPremium;
  // private Integer premiumPendingCount;
  // private Integer paidInstalmentsCount;
  // private List<Instalment> instalments;
  // private BigDecimal oldGrossPremium;
  // private BigDecimal newGrossPremium;
  // private BigDecimal deltaGrossPremium;

  factory DeltaPremium.fromJson(Map<String, dynamic> json) {
    return DeltaPremium(
      pendingPremium: json['pending_premium']?.toDouble(),
      paidAmount: json['paid_amount']?.toDouble(),
      previousGrossPremium: json['previous_gross_premium']?.toDouble(),
      newGrossPremium: json['new_gross_premium']?.toDouble(),
      previousInstallment: json['previous_instalment']?.toDouble(),
      newInstallment: json['new_instalment']?.toDouble(),
      adhocPayment: json['adhoc']?.toDouble(),
      paymentFrequency: json['premium_frequency'],
      premiumPendingCount: json['premium_pending_count'],
      deltaGrossPremium: json['delta_gross_premium'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pending_premium': pendingPremium,
      'paid_amount': paidAmount,
      'previous_gross_premium': previousGrossPremium,
      'new_gross_premium': newGrossPremium,
      'previous_installment': previousInstallment,
      'new_installment': newInstallment,
      'adhoc': adhocPayment,
      'payment_frequency': paymentFrequency,
      'premium_pending_count': premiumPendingCount,
      'delta_gross_premium': deltaGrossPremium,
    };
  }

  bool get isFinancial => adhocPayment != null && adhocPayment! > 0;
}

class EditRequest {
  String? editGroupId;
  String? id;
  String? proposalId;
  String? effectiveDate;
  String? endorsementType;
  String? premiumType;
  String? deltaType;
  List<Endorsements>? endorsements;
  bool? dependent;
  Requirements? requirements;
  String? uwAndDocRules;
  List<String>? dependentRules;
  String? createdAt;
  String? updatedAt;

  EditRequest(
      {this.effectiveDate,
      this.endorsementType,
      this.premiumType,
      this.deltaType,
      this.endorsements,
      this.dependent,
      this.requirements,
      this.uwAndDocRules,
      this.dependentRules,
      this.createdAt,
      this.updatedAt});

  EditRequest.fromJson(Map<String, dynamic> json) {
    editGroupId = json['edit_group_id'];
    id = json['id'];
    proposalId = json['proposal_id'];
    effectiveDate = json['effective_date'];
    endorsementType = json['endorsement_type'];
    premiumType = json['premium_type'];
    deltaType = json['delta_type'];
    if (json['endorsements'] != null) {
      endorsements = <Endorsements>[];
      json['endorsements'].forEach((v) {
        endorsements!.add(new Endorsements.fromJson(v));
      });
    }
    dependent = json['dependent'];

    if (json['requirements'] != null) {
      requirements = Requirements.fromJson(json['requirements']);
    }
    uwAndDocRules = json['uw_and_doc_rules'];
    if (json['dependent_rules'] != null) {
      dependentRules = List<String>.from(json['dependent_rules']);
    }

    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    // data['id'] = this.editGroupId;
    data['edit_group_id'] = editGroupId;
    data['id'] = id;
    data['proposal_id'] = proposalId;
    data['effective_date'] = this.effectiveDate;
    data['endorsement_type'] = this.endorsementType;
    data['premium_type'] = this.premiumType;
    data['delta_type'] = this.deltaType;
    if (this.endorsements != null) {
      data['endorsements'] = this.endorsements!.map((v) => v.toJson()).toList();
    }
    data['dependent'] = this.dependent;
    data['requirements'] = this.requirements;
    data['uw_and_doc_rules'] = this.uwAndDocRules;
    data['dependent_rules'] = this.dependentRules;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }

  String get formattedCreationDate {
    String formattedDate = formatDateString(createdAt ?? '');
    return formattedDate;
  }

  String formatDateString(String dateString) {
    DateTime dateTime = DateTime.parse(dateString).toLocal();
    String formattedDate = DateFormat('dd MMMM yyyy, hh:mm a').format(dateTime);
    return formattedDate;
  }
}

class EditGroup {
  String? editGroupId;
  String? createdAt;
  String? updatedAt;
  String? completedOn;
  String? status;
  List<Edits?>? edits;
  Values? oldValue;
  Values? newValue;

  String? editReferenceId;
  String? proposalId;
  bool? isActive;
  Requirements? requirements;

  EditGroup(
      {this.editGroupId,
      this.createdAt,
      this.updatedAt,
      this.completedOn,
      this.status,
      this.edits,
      this.editReferenceId,
      this.proposalId,
      this.isActive,
      this.oldValue,
      this.newValue,
      this.requirements});

  EditGroup.fromJson(Map<String, dynamic> json) {
    editGroupId = json['id'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    completedOn = json['completed_on'];
    status = json['status'];
    if (json['edits'] != null) {
      edits = <Edits?>[];
      json['edits'].forEach((v) {
        edits!.add(v != null ? Edits.fromJson(v) : null);
      });
    }
    editReferenceId = json['edit_reference_id'];
    proposalId = json['proposal_id'];
    isActive = json['is_active'];
    oldValue =
        json['old_value'] != null ? Values.fromJson(json['old_value']) : null;
    newValue =
        json['new_value'] != null ? Values.fromJson(json['new_value']) : null;
    if (json['requirements'] != null) {
      requirements = Requirements.fromJson(json['requirements']);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = editGroupId;
    // data['edit_group_id'] = editGroupId;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['completed_on'] = completedOn;
    data['status'] = status;
    if (edits != null) {
      data['edits'] = edits!.map((v) => v?.toJson()).toList();
    }
    data['edit_reference_id'] = editReferenceId;
    data['proposal_id'] = proposalId;
    data['is_active'] = isActive;
    data['requirements'] = this.requirements;
    return data;
  }

  String get formattedCreationDate {
    String formattedDate = formatDateString(createdAt ?? '');
    return formattedDate;
  }

  String get formattedUpdationDate {
    String formattedDate = formatDateString(updatedAt ?? '');
    return formattedDate;
  }

  String formatDateString(String dateString) {
    if (dateString.isEmpty) return "";
    DateTime dateTime = DateTime.parse(dateString).toLocal();
    String formattedDate = DateFormat('dd MMMM yyyy, hh:mm a').format(dateTime);
    return formattedDate;
  }
}

class Edits {
  String? effectiveDate;
  String? endorsementType;
  String? premiumType;
  String? deltaType;
  String? editId;
  String? status;
  List<Endorsements>? endorsements;
  bool? dependent;
  String? remark;
  List<ActionRequired>? actionRequired;

  Edits({
    this.effectiveDate,
    this.endorsementType,
    this.premiumType,
    this.deltaType,
    this.editId,
    this.status,
    this.endorsements,
    this.dependent,
    this.remark,
    this.actionRequired,
  });

  Edits.fromJson(Map<String, dynamic> json) {
    effectiveDate = json['effective_date'];
    endorsementType = json['endorsement_type'];
    premiumType = json['premium_type'];
    deltaType = json['delta_type'];
    editId = json['id'];
    // editId = json['edit_id'];
    status = json['status'];
    if (json['endorsements'] != null) {
      endorsements = <Endorsements>[];
      json['endorsements'].forEach((v) {
        endorsements!.add(Endorsements.fromJson(v));
      });
    }
    dependent = json['dependent'] is bool
        ? json['dependent']
        : json['dependent'] == 'true'
            ? true
            : false;
    remark = json['remark'];
    if (json['action_required'] != null) {
      actionRequired = <ActionRequired>[];
      json['action_required'].forEach((v) {
        actionRequired!.add(ActionRequired.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['effective_date'] = effectiveDate;
    data['endorsement_type'] = endorsementType;
    data['premium_type'] = premiumType;
    data['delta_type'] = deltaType;
    data['id'] = editId;
    // data['edit_id'] = editId;
    data['status'] = status;
    if (endorsements != null) {
      data['endorsements'] = endorsements!.map((v) => v.toJson()).toList();
    }
    data['dependent'] = dependent;
    data['remark'] = remark;
    data['action_required'] = actionRequired;
    return data;
  }
}

class Endorsements {
  String? endorsementType;
  dynamic oldValue; // dynamic to support multiple types
  dynamic newValue; // dynamic to support multiple types
  String? path;
  String? deltaType;
  Map<String, dynamic>? parameters;
  Requirements? requirements;
  bool? dependent;
  List<String>? docsRequired;

  Endorsements({
    this.endorsementType,
    this.oldValue,
    this.newValue,
    this.path,
    this.deltaType,
    this.parameters,
    this.requirements,
    this.dependent,
  });

  Endorsements.fromJson(Map<String, dynamic> json) {
    endorsementType = json['endorsement_type'];
    oldValue = json['old_value'];
    newValue = json['new_value'];
    path = json['path'];
    deltaType = json['delta_type'];
    parameters = json['parameters'] != null
        ? Map<String, dynamic>.from(json['parameters'])
        : null;
    requirements = json['requirements'] != null
        ? Requirements.fromJson(json['requirements'])
        : null;
    dependent = json['dependent'];
    // actionRequired = json['action_required'];
    docsRequired = json['docs_required'] != null
        ? List<String>.from(json['docs_required'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['endorsement_type'] = endorsementType;
    data['old_value'] = oldValue;
    data['new_value'] = newValue;
    data['path'] = path;
    data['delta_type'] = deltaType;
    if (parameters != null) {
      data['parameters'] = parameters;
    }
    if (requirements != null) {
      data['requirements'] = requirements!.toJson();
    }
    data['dependent'] = dependent;
    if (docsRequired != null) {
      data['docs_required'] = docsRequired;
    }
    return data;
  }
}

class RenewalFilteredEndorsements {
  List<Endorsements>? memberRemoval;
  List<Endorsements>? personalInfo;
  List<Endorsements>? familyMemberInfo;
  List<Endorsements>? memberAddition;

  RenewalFilteredEndorsements({
    this.memberRemoval,
    this.personalInfo,
    this.familyMemberInfo,
    this.memberAddition,
  });

  RenewalFilteredEndorsements.fromJson(Map<String, dynamic> json) {
    if (json['member_removal'] != null) {
      memberRemoval = <Endorsements>[];
      json['member_removal'].forEach((v) {
        memberRemoval!.add(Endorsements.fromJson(v));
      });
    }
    if (json['personal_info'] != null) {
      personalInfo = <Endorsements>[];
      json['personal_info'].forEach((v) {
        personalInfo!.add(Endorsements.fromJson(v));
      });
    }
    if (json['family_member_info'] != null) {
      familyMemberInfo = <Endorsements>[];
      json['family_member_info'].forEach((v) {
        familyMemberInfo!.add(Endorsements.fromJson(v));
      });
    }
    if (json['member_addition'] != null) {
      memberAddition = <Endorsements>[];
      json['member_addition'].forEach((v) {
        memberAddition!.add(Endorsements.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (memberRemoval != null) {
      data['member_removal'] = memberRemoval!.map((v) => v.toJson()).toList();
    }
    if (personalInfo != null) {
      data['personal_info'] = personalInfo!.map((v) => v.toJson()).toList();
    }
    if (familyMemberInfo != null) {
      data['family_member_info'] =
          familyMemberInfo!.map((v) => v.toJson()).toList();
    }
    if (memberAddition != null) {
      data['member_addition'] = memberAddition!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Requirements {
  List<String>? docsRequired;
  bool? uwReviewRequired;
  bool? highlightRequired;
  bool? rejectProposal;
  bool? reject;

  Requirements({
    this.docsRequired,
    this.uwReviewRequired,
    this.highlightRequired,
    this.rejectProposal,
    this.reject,
  });

  Requirements.fromJson(Map<String, dynamic> json) {
    docsRequired = json['docs_required'] != null
        ? json['docs_required'].cast<String>()
        : null;
    uwReviewRequired = json['uw_review_required'];
    highlightRequired = json['highlight_required'];
    rejectProposal = json['reject_proposal'];
    reject = json['reject'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (docsRequired != null) data['docs_required'] = docsRequired;
    if (uwReviewRequired != null) data['uw_review_required'] = uwReviewRequired;
    if (highlightRequired != null)
      data['highlight_required'] = highlightRequired;
    if (rejectProposal != null) data['reject_proposal'] = rejectProposal;
    if (reject != null) data['reject'] = reject;
    return data;
  }
}

class ActionRequired {
  String? cta;
  String? url;
  String? ctaText;

  ActionRequired({this.cta, this.url, this.ctaText});

  ActionRequired.fromJson(Map<String, dynamic> json) {
    cta = json['cta'];
    url = json['url'];
    ctaText = json['cta_text'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['cta'] = cta;
    data['url'] = url;
    data['cta_text'] = ctaText;
    return data;
  }
}

/// Generic document model!!

class Documents {
  String? id;
  String? entityName;
  String? entityId;
  String? centralReferenceId;
  String? centralReferenceType;
  String? centralDocId;
  String? documentType;
  String? documentFormat;
  String? documentName;
  String? s3Url;
  String? source;
  String? lastModifiedBy;
  Metadata? metadata;
  bool? deleted;
  String? createdAt;
  String? updatedAt;

  String? previewUrl;
  double? progress;

  Documents(
      {this.id,
      this.entityName,
      this.entityId,
      this.centralReferenceId,
      this.centralReferenceType,
      this.centralDocId,
      this.documentType,
      this.documentFormat,
      this.documentName,
      this.s3Url,
      this.source,
      this.lastModifiedBy,
      this.metadata,
      this.deleted,
      this.createdAt,
      this.updatedAt,
      this.previewUrl,
      this.progress});

  void updateProgress(double progress) {
    this.progress = progress;
  }

  Documents.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    entityName = json['entity_name'];
    entityId = json['entity_id'];
    centralReferenceId = json['central_reference_id'];
    centralReferenceType = json['central_reference_type'];
    centralDocId = json['central_doc_id'];
    documentType = json['document_type'];
    documentFormat = json['document_format'];
    documentName = json['document_name'];
    s3Url = json['s3_url'];
    source = json['source'];
    lastModifiedBy = json['last_modified_by'];
    metadata = json['metadata'] != null
        ? new Metadata.fromJson(json['metadata'])
        : null;
    deleted = json['deleted'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    previewUrl = json['preview_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['entity_name'] = this.entityName;
    data['entity_id'] = this.entityId;
    data['central_reference_id'] = this.centralReferenceId;
    data['central_reference_type'] = this.centralReferenceType;
    data['central_doc_id'] = this.centralDocId;
    data['document_type'] = this.documentType;
    data['document_format'] = this.documentFormat;
    data['document_name'] = this.documentName;
    data['s3_url'] = this.s3Url;
    data['source'] = this.source;
    data['last_modified_by'] = this.lastModifiedBy;
    if (this.metadata != null) {
      data['metadata'] = this.metadata!.toJson();
    }
    data['deleted'] = this.deleted;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['preview_url'] = this.previewUrl;
    return data;
  }

  void updateFromUploadedDoc(UploadedDocs uploadedDoc, {String? previewUrl}) {
    id = uploadedDoc.id;
    entityName = uploadedDoc.entityName;
    entityId = uploadedDoc.entityId;
    centralReferenceId = uploadedDoc.centralReferenceId;
    centralReferenceType = uploadedDoc.centralReferenceType;
    centralDocId = uploadedDoc.centralDocId;
    documentType = uploadedDoc.documentType;
    documentFormat = uploadedDoc.documentFormat;
    documentName = uploadedDoc.documentName;
    s3Url = uploadedDoc.s3Url;
    source = uploadedDoc.source;
    lastModifiedBy = uploadedDoc.lastModifiedBy;
    deleted = uploadedDoc.deleted;
    createdAt = uploadedDoc.createdAt;
    updatedAt = uploadedDoc.updatedAt;

    if (previewUrl.isNotNullOrEmpty) this.previewUrl = previewUrl;

    if (uploadedDoc.metadata != null) {
      metadata = Metadata(
          documentName: uploadedDoc.metadata!.documentName,
          proposalId: uploadedDoc.metadata!.proposalId,
          fileName: uploadedDoc.metadata!.fileName,
          allowDuplicate: uploadedDoc.metadata!.allowDuplicate,
          documentCategory: uploadedDoc.metadata!.documentCategory,
          memberUniqueId: uploadedDoc.metadata!.memberUniqueId,
          description: uploadedDoc.metadata!.description,
          documentFormat: uploadedDoc.metadata!.documentFormat,
          editId: uploadedDoc.metadata!.editId,
          editGroupId: uploadedDoc.metadata!.editGroupId,
          endorsementType: uploadedDoc.metadata!.endorsementType);
    }
  }
}

class Metadata {
  String? documentName;
  String? proposalId;
  String? fileName;
  bool? allowDuplicate;
  String? documentCategory;
  String? memberUniqueId;
  String? description;
  String? documentFormat;
  String? editId;
  String? editGroupId;
  String? endorsementType;

  Metadata(
      {this.documentName,
      this.proposalId,
      this.fileName,
      this.allowDuplicate,
      this.documentCategory,
      this.memberUniqueId,
      this.description,
      this.documentFormat,
      this.editId,
      this.editGroupId,
      this.endorsementType});

  Metadata.fromJson(Map<String, dynamic> json) {
    documentName = json['document_name'];
    proposalId = json['proposal_id'];
    fileName = json['file_name'];
    allowDuplicate = json['allow_duplicate'];
    documentCategory = json['document_category'];
    memberUniqueId = json['member_unique_id'];
    description = json['description'];
    documentFormat = json['document_format'];
    editId = json['edit_id'];
    editGroupId = json['edit_group_id'];
    endorsementType = json['endorsement_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['document_name'] = this.documentName;
    data['proposal_id'] = this.proposalId;
    data['file_name'] = this.fileName;
    data['allow_duplicate'] = this.allowDuplicate;
    data['document_category'] = this.documentCategory;
    data['member_unique_id'] = this.memberUniqueId;
    data['description'] = this.description;
    data['document_format'] = this.documentFormat;
    data['edit_id'] = this.editId;
    data['edit_group_id'] = this.editGroupId;
    data['endorsement_type'] = this.endorsementType;
    return data;
  }
}

extension HealthJourneyManagerResponseX on HealthJourneyManagerResponse {
  bool get showForceLogin => authorised == false;

  ForceLoginModel get getForceLoginModel => ForceLoginModel(
        phoneNumber: phoneNumber ?? '',
        stateUrl: stateUrl ?? '',
        customMessage: customMessage,
        ctaText: cta,
      );
}

class InputData {
  String? title;
  String? subTitle;
  int? maxMandateAmount;
  int? mandateMaxYears;
  bool? isMandateEnabled;
  int? mandateMultiplier;

  InputData({
    this.title,
    this.subTitle,
    this.maxMandateAmount,
    this.mandateMaxYears,
    this.isMandateEnabled,
    this.mandateMultiplier,
  });

  factory InputData.fromJson(Map<String, dynamic> json) {
    return InputData(
      title: json['title'],
      subTitle: json['sub_title'],
      maxMandateAmount: json['max_mandate_amount'],
      mandateMaxYears: json['mandate_max_years'],
      isMandateEnabled: json['is_mandate_enabled'],
      mandateMultiplier: json['mandate_multiplier'],
    );
  }
}
